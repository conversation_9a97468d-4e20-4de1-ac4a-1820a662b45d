  AlarmManager android.app  )ForegroundServiceStartNotAllowedException android.app  Notification android.app  NotificationChannel android.app  NotificationManager android.app  
PendingIntent android.app  Service android.app  
RTC_WAKEUP android.app.AlarmManager  cancel android.app.AlarmManager  set android.app.AlarmManager  setExact android.app.AlarmManager  setExactAndAllowWhileIdle android.app.AlarmManager  Builder android.app.Notification  FOREGROUND_SERVICE_IMMEDIATE android.app.Notification  build  android.app.Notification.Builder  
setAutoCancel  android.app.Notification.Builder  setCategory  android.app.Notification.Builder  setContentIntent  android.app.Notification.Builder  setContentText  android.app.Notification.Builder  setContentTitle  android.app.Notification.Builder  setForegroundServiceBehavior  android.app.Notification.Builder  setFullScreenIntent  android.app.Notification.Builder  
setOngoing  android.app.Notification.Builder  setPriority  android.app.Notification.Builder  setSmallIcon  android.app.Notification.Builder  setSound  android.app.Notification.Builder  
setVisibility  android.app.Notification.Builder  apply android.app.NotificationChannel  description android.app.NotificationChannel  getAPPLY android.app.NotificationChannel  getApply android.app.NotificationChannel  getDESCRIPTION android.app.NotificationChannel  getDescription android.app.NotificationChannel  setDescription android.app.NotificationChannel  setSound android.app.NotificationChannel  IMPORTANCE_HIGH android.app.NotificationManager  createNotificationChannel android.app.NotificationManager  notify android.app.NotificationManager  FLAG_IMMUTABLE android.app.PendingIntent  FLAG_UPDATE_CURRENT android.app.PendingIntent  getActivity android.app.PendingIntent  getBroadcast android.app.PendingIntent  getForegroundService android.app.PendingIntent  send android.app.PendingIntent  AlarmPlugin android.app.Service  AudioService android.app.Service  Boolean android.app.Service  Build android.app.Service  Context android.app.Service  	Exception android.app.Service  )ForegroundServiceStartNotAllowedException android.app.Service  IBinder android.app.Service  IllegalStateException android.app.Service  Int android.app.Service  Intent android.app.Service  	JvmStatic android.app.Service  List android.app.Service  Log android.app.Service  NotificationChannel android.app.Service  NotificationCompat android.app.Service  NotificationHandler android.app.Service  NotificationManager android.app.Service  
PendingIntent android.app.Service  PowerManager android.app.Service  RequiresApi android.app.Service  START_NOT_STICKY android.app.Service  START_STICKY android.app.Service  SecurityException android.app.Service  ServiceInfo android.app.Service  Settings android.app.Service  String android.app.Service  VibrationService android.app.Service  
VolumeService android.app.Service  apply android.app.Service  getSystemService android.app.Service  invoke android.app.Service  listOf android.app.Service  longArrayOf android.app.Service  mapOf android.app.Service  onCreate android.app.Service  	onDestroy android.app.Service  
onTaskRemoved android.app.Service  ringingAlarmIds android.app.Service  startForeground android.app.Service  	stopAlarm android.app.Service  stopForeground android.app.Service  stopSelf android.app.Service  to android.app.Service  BroadcastReceiver android.content  
ComponentName android.content  Context android.content  Intent android.content  AlarmService !android.content.BroadcastReceiver  Build !android.content.BroadcastReceiver  Context !android.content.BroadcastReceiver  Intent !android.content.BroadcastReceiver  
PendingIntent !android.content.BroadcastReceiver  java !android.content.BroadcastReceiver  
ALARM_SERVICE android.content.Context  
AUDIO_SERVICE android.content.Context  AlarmPlugin android.content.Context  AudioService android.content.Context  Boolean android.content.Context  Build android.content.Context  Context android.content.Context  	Exception android.content.Context  )ForegroundServiceStartNotAllowedException android.content.Context  IBinder android.content.Context  IllegalStateException android.content.Context  Int android.content.Context  Intent android.content.Context  	JvmStatic android.content.Context  List android.content.Context  Log android.content.Context  NOTIFICATION_SERVICE android.content.Context  NotificationChannel android.content.Context  NotificationCompat android.content.Context  NotificationHandler android.content.Context  NotificationManager android.content.Context  
POWER_SERVICE android.content.Context  
PendingIntent android.content.Context  PowerManager android.content.Context  RequiresApi android.content.Context  START_NOT_STICKY android.content.Context  START_STICKY android.content.Context  SecurityException android.content.Context  ServiceInfo android.content.Context  Settings android.content.Context  String android.content.Context  VIBRATOR_SERVICE android.content.Context  VibrationService android.content.Context  
VolumeService android.content.Context  apply android.content.Context  assets android.content.Context  filesDir android.content.Context  	getASSETS android.content.Context  	getAssets android.content.Context  getFILESDir android.content.Context  getFilesDir android.content.Context  getPACKAGEManager android.content.Context  getPACKAGEName android.content.Context  getPackageManager android.content.Context  getPackageName android.content.Context  getSystemService android.content.Context  invoke android.content.Context  listOf android.content.Context  longArrayOf android.content.Context  mapOf android.content.Context  onCreate android.content.Context  	onDestroy android.content.Context  
onTaskRemoved android.content.Context  packageManager android.content.Context  packageName android.content.Context  ringingAlarmIds android.content.Context  
sendBroadcast android.content.Context  	setAssets android.content.Context  setFilesDir android.content.Context  setPackageManager android.content.Context  setPackageName android.content.Context  startForeground android.content.Context  startForegroundService android.content.Context  startService android.content.Context  	stopAlarm android.content.Context  stopForeground android.content.Context  stopSelf android.content.Context  stopService android.content.Context  to android.content.Context  AlarmPlugin android.content.ContextWrapper  AudioService android.content.ContextWrapper  Boolean android.content.ContextWrapper  Build android.content.ContextWrapper  Context android.content.ContextWrapper  	Exception android.content.ContextWrapper  )ForegroundServiceStartNotAllowedException android.content.ContextWrapper  IBinder android.content.ContextWrapper  IllegalStateException android.content.ContextWrapper  Int android.content.ContextWrapper  Intent android.content.ContextWrapper  	JvmStatic android.content.ContextWrapper  List android.content.ContextWrapper  Log android.content.ContextWrapper  NotificationChannel android.content.ContextWrapper  NotificationCompat android.content.ContextWrapper  NotificationHandler android.content.ContextWrapper  NotificationManager android.content.ContextWrapper  
PendingIntent android.content.ContextWrapper  PowerManager android.content.ContextWrapper  RequiresApi android.content.ContextWrapper  START_NOT_STICKY android.content.ContextWrapper  START_STICKY android.content.ContextWrapper  SecurityException android.content.ContextWrapper  ServiceInfo android.content.ContextWrapper  Settings android.content.ContextWrapper  String android.content.ContextWrapper  VibrationService android.content.ContextWrapper  
VolumeService android.content.ContextWrapper  apply android.content.ContextWrapper  getSystemService android.content.ContextWrapper  invoke android.content.ContextWrapper  listOf android.content.ContextWrapper  longArrayOf android.content.ContextWrapper  mapOf android.content.ContextWrapper  onCreate android.content.ContextWrapper  	onDestroy android.content.ContextWrapper  
onTaskRemoved android.content.ContextWrapper  ringingAlarmIds android.content.ContextWrapper  startForeground android.content.ContextWrapper  	stopAlarm android.content.ContextWrapper  stopForeground android.content.ContextWrapper  stopSelf android.content.ContextWrapper  to android.content.ContextWrapper  action android.content.Intent  equals android.content.Intent  	getACTION android.content.Intent  	getAction android.content.Intent  getBooleanExtra android.content.Intent  getDoubleExtra android.content.Intent  getIntExtra android.content.Intent  getStringExtra android.content.Intent  putExtra android.content.Intent  	putExtras android.content.Intent  	setAction android.content.Intent  PackageManager android.content.pm  ServiceInfo android.content.pm  icon "android.content.pm.ApplicationInfo  getApplicationInfo !android.content.pm.PackageManager  getLaunchIntentForPackage !android.content.pm.PackageManager  &FOREGROUND_SERVICE_TYPE_MEDIA_PLAYBACK android.content.pm.ServiceInfo  AssetFileDescriptor android.content.res  AssetManager android.content.res  fileDescriptor 'android.content.res.AssetFileDescriptor  getFILEDescriptor 'android.content.res.AssetFileDescriptor  getFileDescriptor 'android.content.res.AssetFileDescriptor  	getLENGTH 'android.content.res.AssetFileDescriptor  	getLength 'android.content.res.AssetFileDescriptor  getSTARTOffset 'android.content.res.AssetFileDescriptor  getStartOffset 'android.content.res.AssetFileDescriptor  length 'android.content.res.AssetFileDescriptor  setFileDescriptor 'android.content.res.AssetFileDescriptor  	setLength 'android.content.res.AssetFileDescriptor  setStartOffset 'android.content.res.AssetFileDescriptor  startOffset 'android.content.res.AssetFileDescriptor  openFd  android.content.res.AssetManager  
BitmapFactory android.graphics  Drawable android.graphics.drawable  AudioAttributes 
android.media  AudioFocusRequest 
android.media  AudioManager 
android.media  MediaPlayer 
android.media  Builder android.media.AudioAttributes  CONTENT_TYPE_SONIFICATION android.media.AudioAttributes  USAGE_ALARM android.media.AudioAttributes  build %android.media.AudioAttributes.Builder  setContentType %android.media.AudioAttributes.Builder  setUsage %android.media.AudioAttributes.Builder  Builder android.media.AudioFocusRequest  getLET android.media.AudioFocusRequest  getLet android.media.AudioFocusRequest  let android.media.AudioFocusRequest  build 'android.media.AudioFocusRequest.Builder  setAudioAttributes 'android.media.AudioFocusRequest.Builder  "AUDIOFOCUS_GAIN_TRANSIENT_MAY_DUCK android.media.AudioManager  AUDIOFOCUS_REQUEST_GRANTED android.media.AudioManager  FLAG_SHOW_UI android.media.AudioManager  STREAM_ALARM android.media.AudioManager  STREAM_MUSIC android.media.AudioManager  abandonAudioFocus android.media.AudioManager  abandonAudioFocusRequest android.media.AudioManager  getStreamMaxVolume android.media.AudioManager  getStreamVolume android.media.AudioManager  requestAudioFocus android.media.AudioManager  setStreamVolume android.media.AudioManager  Timer android.media.MediaPlayer  apply android.media.MediaPlayer  context android.media.MediaPlayer  getAPPLY android.media.MediaPlayer  getApply android.media.MediaPlayer  
getCONTEXT android.media.MediaPlayer  
getContext android.media.MediaPlayer  getISLooping android.media.MediaPlayer  getISPlaying android.media.MediaPlayer  getIsLooping android.media.MediaPlayer  getIsPlaying android.media.MediaPlayer  getMEDIAPlayers android.media.MediaPlayer  getMediaPlayers android.media.MediaPlayer  getONAudioComplete android.media.MediaPlayer  getOnAudioComplete android.media.MediaPlayer  getSET android.media.MediaPlayer  getSTARTFadeIn android.media.MediaPlayer  
getSTARTSWith android.media.MediaPlayer  getSet android.media.MediaPlayer  getStartFadeIn android.media.MediaPlayer  
getStartsWith android.media.MediaPlayer  	getTIMERS android.media.MediaPlayer  	getTimers android.media.MediaPlayer  	isLooping android.media.MediaPlayer  	isPlaying android.media.MediaPlayer  mediaPlayers android.media.MediaPlayer  onAudioComplete android.media.MediaPlayer  prepare android.media.MediaPlayer  release android.media.MediaPlayer  reset android.media.MediaPlayer  set android.media.MediaPlayer  
setDataSource android.media.MediaPlayer  
setLooping android.media.MediaPlayer  setOnCompletionListener android.media.MediaPlayer  
setPlaying android.media.MediaPlayer  	setVolume android.media.MediaPlayer  start android.media.MediaPlayer  startFadeIn android.media.MediaPlayer  
startsWith android.media.MediaPlayer  stop android.media.MediaPlayer  timers android.media.MediaPlayer  <SAM-CONSTRUCTOR> .android.media.MediaPlayer.OnCompletionListener  Uri android.net  Build 
android.os  Handler 
android.os  IBinder 
android.os  Looper 
android.os  PowerManager 
android.os  VibrationEffect 
android.os  Vibrator 
android.os  VERSION android.os.Build  
VERSION_CODES android.os.Build  SDK_INT android.os.Build.VERSION  KITKAT android.os.Build.VERSION_CODES  M android.os.Build.VERSION_CODES  O android.os.Build.VERSION_CODES  R android.os.Build.VERSION_CODES  S android.os.Build.VERSION_CODES  postDelayed android.os.Handler  
getMainLooper android.os.Looper  PARTIAL_WAKE_LOCK android.os.PowerManager  WakeLock android.os.PowerManager  newWakeLock android.os.PowerManager  acquire  android.os.PowerManager.WakeLock  createWaveform android.os.VibrationEffect  cancel android.os.Vibrator  vibrate android.os.Vibrator  Settings android.provider  System android.provider.Settings  DEFAULT_ALARM_ALERT_URI  android.provider.Settings.System  NonNull androidx.annotation  RequiresApi androidx.annotation  NotificationCompat androidx.core.app  Builder $androidx.core.app.NotificationCompat  CATEGORY_ALARM $androidx.core.app.NotificationCompat  PRIORITY_MAX $androidx.core.app.NotificationCompat  VISIBILITY_PUBLIC $androidx.core.app.NotificationCompat  build ,androidx.core.app.NotificationCompat.Builder  
setAutoCancel ,androidx.core.app.NotificationCompat.Builder  setContentIntent ,androidx.core.app.NotificationCompat.Builder  setContentText ,androidx.core.app.NotificationCompat.Builder  setContentTitle ,androidx.core.app.NotificationCompat.Builder  setPriority ,androidx.core.app.NotificationCompat.Builder  setSmallIcon ,androidx.core.app.NotificationCompat.Builder  setSound ,androidx.core.app.NotificationCompat.Builder  AlarmManager com.gdelataillade.alarm.alarm  AlarmPlugin com.gdelataillade.alarm.alarm  
AlarmReceiver com.gdelataillade.alarm.alarm  AlarmService com.gdelataillade.alarm.alarm  Any com.gdelataillade.alarm.alarm  AudioService com.gdelataillade.alarm.alarm  Boolean com.gdelataillade.alarm.alarm  Build com.gdelataillade.alarm.alarm  
CHANNEL_ID com.gdelataillade.alarm.alarm  CHANNEL_NAME com.gdelataillade.alarm.alarm  ClassCastException com.gdelataillade.alarm.alarm  Context com.gdelataillade.alarm.alarm  Double com.gdelataillade.alarm.alarm  EventChannel com.gdelataillade.alarm.alarm  	Exception com.gdelataillade.alarm.alarm  Handler com.gdelataillade.alarm.alarm  IllegalStateException com.gdelataillade.alarm.alarm  Int com.gdelataillade.alarm.alarm  Intent com.gdelataillade.alarm.alarm  	JvmStatic com.gdelataillade.alarm.alarm  List com.gdelataillade.alarm.alarm  Log com.gdelataillade.alarm.alarm  Looper com.gdelataillade.alarm.alarm  
MethodChannel com.gdelataillade.alarm.alarm  Notification com.gdelataillade.alarm.alarm  NotificationChannel com.gdelataillade.alarm.alarm  NotificationCompat com.gdelataillade.alarm.alarm  NotificationHandler com.gdelataillade.alarm.alarm  NotificationManager com.gdelataillade.alarm.alarm  NotificationOnKillService com.gdelataillade.alarm.alarm  
PendingIntent com.gdelataillade.alarm.alarm  PowerManager com.gdelataillade.alarm.alarm  START_NOT_STICKY com.gdelataillade.alarm.alarm  START_STICKY com.gdelataillade.alarm.alarm  SecurityException com.gdelataillade.alarm.alarm  ServiceInfo com.gdelataillade.alarm.alarm  String com.gdelataillade.alarm.alarm  System com.gdelataillade.alarm.alarm  VibrationService com.gdelataillade.alarm.alarm  
VolumeService com.gdelataillade.alarm.alarm  apply com.gdelataillade.alarm.alarm  contains com.gdelataillade.alarm.alarm  	eventSink com.gdelataillade.alarm.alarm  invoke com.gdelataillade.alarm.alarm  java com.gdelataillade.alarm.alarm  listOf com.gdelataillade.alarm.alarm  longArrayOf com.gdelataillade.alarm.alarm  mapOf com.gdelataillade.alarm.alarm  ringingAlarmIds com.gdelataillade.alarm.alarm  to com.gdelataillade.alarm.alarm  AlarmManager )com.gdelataillade.alarm.alarm.AlarmPlugin  
AlarmReceiver )com.gdelataillade.alarm.alarm.AlarmPlugin  AlarmService )com.gdelataillade.alarm.alarm.AlarmPlugin  Any )com.gdelataillade.alarm.alarm.AlarmPlugin  Boolean )com.gdelataillade.alarm.alarm.AlarmPlugin  Build )com.gdelataillade.alarm.alarm.AlarmPlugin  ClassCastException )com.gdelataillade.alarm.alarm.AlarmPlugin  Context )com.gdelataillade.alarm.alarm.AlarmPlugin  Double )com.gdelataillade.alarm.alarm.AlarmPlugin  EventChannel )com.gdelataillade.alarm.alarm.AlarmPlugin  	Exception )com.gdelataillade.alarm.alarm.AlarmPlugin  
FlutterPlugin )com.gdelataillade.alarm.alarm.AlarmPlugin  Handler )com.gdelataillade.alarm.alarm.AlarmPlugin  IllegalStateException )com.gdelataillade.alarm.alarm.AlarmPlugin  Int )com.gdelataillade.alarm.alarm.AlarmPlugin  Intent )com.gdelataillade.alarm.alarm.AlarmPlugin  	JvmStatic )com.gdelataillade.alarm.alarm.AlarmPlugin  Log )com.gdelataillade.alarm.alarm.AlarmPlugin  Looper )com.gdelataillade.alarm.alarm.AlarmPlugin  
MethodCall )com.gdelataillade.alarm.alarm.AlarmPlugin  
MethodChannel )com.gdelataillade.alarm.alarm.AlarmPlugin  NonNull )com.gdelataillade.alarm.alarm.AlarmPlugin  NotificationOnKillService )com.gdelataillade.alarm.alarm.AlarmPlugin  
PendingIntent )com.gdelataillade.alarm.alarm.AlarmPlugin  Result )com.gdelataillade.alarm.alarm.AlarmPlugin  String )com.gdelataillade.alarm.alarm.AlarmPlugin  System )com.gdelataillade.alarm.alarm.AlarmPlugin  contains )com.gdelataillade.alarm.alarm.AlarmPlugin  context )com.gdelataillade.alarm.alarm.AlarmPlugin  createAlarmIntent )com.gdelataillade.alarm.alarm.AlarmPlugin  eventChannel )com.gdelataillade.alarm.alarm.AlarmPlugin  	eventSink )com.gdelataillade.alarm.alarm.AlarmPlugin  getCONTAINS )com.gdelataillade.alarm.alarm.AlarmPlugin  getContains )com.gdelataillade.alarm.alarm.AlarmPlugin  getEVENTSink )com.gdelataillade.alarm.alarm.AlarmPlugin  getEventSink )com.gdelataillade.alarm.alarm.AlarmPlugin  handleDelayedAlarm )com.gdelataillade.alarm.alarm.AlarmPlugin  handleImmediateAlarm )com.gdelataillade.alarm.alarm.AlarmPlugin  java )com.gdelataillade.alarm.alarm.AlarmPlugin  
methodChannel )com.gdelataillade.alarm.alarm.AlarmPlugin  setIntentExtras )com.gdelataillade.alarm.alarm.AlarmPlugin  AlarmManager 3com.gdelataillade.alarm.alarm.AlarmPlugin.Companion  
AlarmReceiver 3com.gdelataillade.alarm.alarm.AlarmPlugin.Companion  AlarmService 3com.gdelataillade.alarm.alarm.AlarmPlugin.Companion  Any 3com.gdelataillade.alarm.alarm.AlarmPlugin.Companion  Boolean 3com.gdelataillade.alarm.alarm.AlarmPlugin.Companion  Build 3com.gdelataillade.alarm.alarm.AlarmPlugin.Companion  ClassCastException 3com.gdelataillade.alarm.alarm.AlarmPlugin.Companion  Context 3com.gdelataillade.alarm.alarm.AlarmPlugin.Companion  Double 3com.gdelataillade.alarm.alarm.AlarmPlugin.Companion  EventChannel 3com.gdelataillade.alarm.alarm.AlarmPlugin.Companion  	Exception 3com.gdelataillade.alarm.alarm.AlarmPlugin.Companion  
FlutterPlugin 3com.gdelataillade.alarm.alarm.AlarmPlugin.Companion  Handler 3com.gdelataillade.alarm.alarm.AlarmPlugin.Companion  IllegalStateException 3com.gdelataillade.alarm.alarm.AlarmPlugin.Companion  Int 3com.gdelataillade.alarm.alarm.AlarmPlugin.Companion  Intent 3com.gdelataillade.alarm.alarm.AlarmPlugin.Companion  	JvmStatic 3com.gdelataillade.alarm.alarm.AlarmPlugin.Companion  Log 3com.gdelataillade.alarm.alarm.AlarmPlugin.Companion  Looper 3com.gdelataillade.alarm.alarm.AlarmPlugin.Companion  
MethodCall 3com.gdelataillade.alarm.alarm.AlarmPlugin.Companion  
MethodChannel 3com.gdelataillade.alarm.alarm.AlarmPlugin.Companion  NonNull 3com.gdelataillade.alarm.alarm.AlarmPlugin.Companion  NotificationOnKillService 3com.gdelataillade.alarm.alarm.AlarmPlugin.Companion  
PendingIntent 3com.gdelataillade.alarm.alarm.AlarmPlugin.Companion  Result 3com.gdelataillade.alarm.alarm.AlarmPlugin.Companion  String 3com.gdelataillade.alarm.alarm.AlarmPlugin.Companion  System 3com.gdelataillade.alarm.alarm.AlarmPlugin.Companion  contains 3com.gdelataillade.alarm.alarm.AlarmPlugin.Companion  	eventSink 3com.gdelataillade.alarm.alarm.AlarmPlugin.Companion  getCONTAINS 3com.gdelataillade.alarm.alarm.AlarmPlugin.Companion  getContains 3com.gdelataillade.alarm.alarm.AlarmPlugin.Companion  java 3com.gdelataillade.alarm.alarm.AlarmPlugin.Companion  getEVENTSink Ocom.gdelataillade.alarm.alarm.AlarmPlugin.onAttachedToEngine.<no name provided>  getEventSink Ocom.gdelataillade.alarm.alarm.AlarmPlugin.onAttachedToEngine.<no name provided>  AlarmService +com.gdelataillade.alarm.alarm.AlarmReceiver  Build +com.gdelataillade.alarm.alarm.AlarmReceiver  Context +com.gdelataillade.alarm.alarm.AlarmReceiver  Intent +com.gdelataillade.alarm.alarm.AlarmReceiver  
PendingIntent +com.gdelataillade.alarm.alarm.AlarmReceiver  java +com.gdelataillade.alarm.alarm.AlarmReceiver  AlarmPlugin *com.gdelataillade.alarm.alarm.AlarmService  AudioService *com.gdelataillade.alarm.alarm.AlarmService  Boolean *com.gdelataillade.alarm.alarm.AlarmService  Build *com.gdelataillade.alarm.alarm.AlarmService  	Companion *com.gdelataillade.alarm.alarm.AlarmService  Context *com.gdelataillade.alarm.alarm.AlarmService  	Exception *com.gdelataillade.alarm.alarm.AlarmService  )ForegroundServiceStartNotAllowedException *com.gdelataillade.alarm.alarm.AlarmService  IBinder *com.gdelataillade.alarm.alarm.AlarmService  IllegalStateException *com.gdelataillade.alarm.alarm.AlarmService  Int *com.gdelataillade.alarm.alarm.AlarmService  Intent *com.gdelataillade.alarm.alarm.AlarmService  	JvmStatic *com.gdelataillade.alarm.alarm.AlarmService  List *com.gdelataillade.alarm.alarm.AlarmService  Log *com.gdelataillade.alarm.alarm.AlarmService  NotificationHandler *com.gdelataillade.alarm.alarm.AlarmService  
PendingIntent *com.gdelataillade.alarm.alarm.AlarmService  PowerManager *com.gdelataillade.alarm.alarm.AlarmService  START_NOT_STICKY *com.gdelataillade.alarm.alarm.AlarmService  START_STICKY *com.gdelataillade.alarm.alarm.AlarmService  SecurityException *com.gdelataillade.alarm.alarm.AlarmService  ServiceInfo *com.gdelataillade.alarm.alarm.AlarmService  VibrationService *com.gdelataillade.alarm.alarm.AlarmService  
VolumeService *com.gdelataillade.alarm.alarm.AlarmService  applicationContext *com.gdelataillade.alarm.alarm.AlarmService  audioService *com.gdelataillade.alarm.alarm.AlarmService  getAPPLICATIONContext *com.gdelataillade.alarm.alarm.AlarmService  getApplicationContext *com.gdelataillade.alarm.alarm.AlarmService  	getLISTOf *com.gdelataillade.alarm.alarm.AlarmService  getLONGArrayOf *com.gdelataillade.alarm.alarm.AlarmService  	getListOf *com.gdelataillade.alarm.alarm.AlarmService  getLongArrayOf *com.gdelataillade.alarm.alarm.AlarmService  getMAPOf *com.gdelataillade.alarm.alarm.AlarmService  getMapOf *com.gdelataillade.alarm.alarm.AlarmService  getRINGINGAlarmIds *com.gdelataillade.alarm.alarm.AlarmService  getRingingAlarmIds *com.gdelataillade.alarm.alarm.AlarmService  getSystemService *com.gdelataillade.alarm.alarm.AlarmService  getTO *com.gdelataillade.alarm.alarm.AlarmService  getTo *com.gdelataillade.alarm.alarm.AlarmService  invoke *com.gdelataillade.alarm.alarm.AlarmService  listOf *com.gdelataillade.alarm.alarm.AlarmService  longArrayOf *com.gdelataillade.alarm.alarm.AlarmService  mapOf *com.gdelataillade.alarm.alarm.AlarmService  ringingAlarmIds *com.gdelataillade.alarm.alarm.AlarmService  setApplicationContext *com.gdelataillade.alarm.alarm.AlarmService  showSystemUI *com.gdelataillade.alarm.alarm.AlarmService  startForeground *com.gdelataillade.alarm.alarm.AlarmService  	stopAlarm *com.gdelataillade.alarm.alarm.AlarmService  stopForeground *com.gdelataillade.alarm.alarm.AlarmService  stopSelf *com.gdelataillade.alarm.alarm.AlarmService  to *com.gdelataillade.alarm.alarm.AlarmService  vibrationService *com.gdelataillade.alarm.alarm.AlarmService  
volumeService *com.gdelataillade.alarm.alarm.AlarmService  AlarmPlugin 4com.gdelataillade.alarm.alarm.AlarmService.Companion  AudioService 4com.gdelataillade.alarm.alarm.AlarmService.Companion  Boolean 4com.gdelataillade.alarm.alarm.AlarmService.Companion  Build 4com.gdelataillade.alarm.alarm.AlarmService.Companion  Context 4com.gdelataillade.alarm.alarm.AlarmService.Companion  	Exception 4com.gdelataillade.alarm.alarm.AlarmService.Companion  )ForegroundServiceStartNotAllowedException 4com.gdelataillade.alarm.alarm.AlarmService.Companion  IBinder 4com.gdelataillade.alarm.alarm.AlarmService.Companion  IllegalStateException 4com.gdelataillade.alarm.alarm.AlarmService.Companion  Int 4com.gdelataillade.alarm.alarm.AlarmService.Companion  Intent 4com.gdelataillade.alarm.alarm.AlarmService.Companion  	JvmStatic 4com.gdelataillade.alarm.alarm.AlarmService.Companion  List 4com.gdelataillade.alarm.alarm.AlarmService.Companion  Log 4com.gdelataillade.alarm.alarm.AlarmService.Companion  NotificationHandler 4com.gdelataillade.alarm.alarm.AlarmService.Companion  
PendingIntent 4com.gdelataillade.alarm.alarm.AlarmService.Companion  PowerManager 4com.gdelataillade.alarm.alarm.AlarmService.Companion  START_NOT_STICKY 4com.gdelataillade.alarm.alarm.AlarmService.Companion  START_STICKY 4com.gdelataillade.alarm.alarm.AlarmService.Companion  SecurityException 4com.gdelataillade.alarm.alarm.AlarmService.Companion  ServiceInfo 4com.gdelataillade.alarm.alarm.AlarmService.Companion  VibrationService 4com.gdelataillade.alarm.alarm.AlarmService.Companion  
VolumeService 4com.gdelataillade.alarm.alarm.AlarmService.Companion  	getLISTOf 4com.gdelataillade.alarm.alarm.AlarmService.Companion  getLONGArrayOf 4com.gdelataillade.alarm.alarm.AlarmService.Companion  	getListOf 4com.gdelataillade.alarm.alarm.AlarmService.Companion  getLongArrayOf 4com.gdelataillade.alarm.alarm.AlarmService.Companion  getMAPOf 4com.gdelataillade.alarm.alarm.AlarmService.Companion  getMapOf 4com.gdelataillade.alarm.alarm.AlarmService.Companion  getTO 4com.gdelataillade.alarm.alarm.AlarmService.Companion  getTo 4com.gdelataillade.alarm.alarm.AlarmService.Companion  invoke 4com.gdelataillade.alarm.alarm.AlarmService.Companion  listOf 4com.gdelataillade.alarm.alarm.AlarmService.Companion  longArrayOf 4com.gdelataillade.alarm.alarm.AlarmService.Companion  mapOf 4com.gdelataillade.alarm.alarm.AlarmService.Companion  ringingAlarmIds 4com.gdelataillade.alarm.alarm.AlarmService.Companion  to 4com.gdelataillade.alarm.alarm.AlarmService.Companion  Boolean 1com.gdelataillade.alarm.alarm.NotificationHandler  Build 1com.gdelataillade.alarm.alarm.NotificationHandler  
CHANNEL_ID 1com.gdelataillade.alarm.alarm.NotificationHandler  CHANNEL_NAME 1com.gdelataillade.alarm.alarm.NotificationHandler  Context 1com.gdelataillade.alarm.alarm.NotificationHandler  Intent 1com.gdelataillade.alarm.alarm.NotificationHandler  Notification 1com.gdelataillade.alarm.alarm.NotificationHandler  NotificationChannel 1com.gdelataillade.alarm.alarm.NotificationHandler  NotificationCompat 1com.gdelataillade.alarm.alarm.NotificationHandler  NotificationManager 1com.gdelataillade.alarm.alarm.NotificationHandler  
PendingIntent 1com.gdelataillade.alarm.alarm.NotificationHandler  String 1com.gdelataillade.alarm.alarm.NotificationHandler  apply 1com.gdelataillade.alarm.alarm.NotificationHandler  buildNotification 1com.gdelataillade.alarm.alarm.NotificationHandler  context 1com.gdelataillade.alarm.alarm.NotificationHandler  createNotificationChannel 1com.gdelataillade.alarm.alarm.NotificationHandler  getAPPLY 1com.gdelataillade.alarm.alarm.NotificationHandler  getApply 1com.gdelataillade.alarm.alarm.NotificationHandler  Boolean ;com.gdelataillade.alarm.alarm.NotificationHandler.Companion  Build ;com.gdelataillade.alarm.alarm.NotificationHandler.Companion  
CHANNEL_ID ;com.gdelataillade.alarm.alarm.NotificationHandler.Companion  CHANNEL_NAME ;com.gdelataillade.alarm.alarm.NotificationHandler.Companion  Context ;com.gdelataillade.alarm.alarm.NotificationHandler.Companion  Intent ;com.gdelataillade.alarm.alarm.NotificationHandler.Companion  Notification ;com.gdelataillade.alarm.alarm.NotificationHandler.Companion  NotificationChannel ;com.gdelataillade.alarm.alarm.NotificationHandler.Companion  NotificationCompat ;com.gdelataillade.alarm.alarm.NotificationHandler.Companion  NotificationManager ;com.gdelataillade.alarm.alarm.NotificationHandler.Companion  
PendingIntent ;com.gdelataillade.alarm.alarm.NotificationHandler.Companion  String ;com.gdelataillade.alarm.alarm.NotificationHandler.Companion  apply ;com.gdelataillade.alarm.alarm.NotificationHandler.Companion  getAPPLY ;com.gdelataillade.alarm.alarm.NotificationHandler.Companion  getApply ;com.gdelataillade.alarm.alarm.NotificationHandler.Companion  invoke ;com.gdelataillade.alarm.alarm.NotificationHandler.Companion  AudioAttributes  com.gdelataillade.alarm.services  AudioFocusRequest  com.gdelataillade.alarm.services  AudioManager  com.gdelataillade.alarm.services  AudioService  com.gdelataillade.alarm.services  Boolean  com.gdelataillade.alarm.services  Build  com.gdelataillade.alarm.services  ConcurrentHashMap  com.gdelataillade.alarm.services  Context  com.gdelataillade.alarm.services  Double  com.gdelataillade.alarm.services  	Exception  com.gdelataillade.alarm.services  Int  com.gdelataillade.alarm.services  List  com.gdelataillade.alarm.services  Log  com.gdelataillade.alarm.services  	LongArray  com.gdelataillade.alarm.services  MediaPlayer  com.gdelataillade.alarm.services  NotificationChannel  com.gdelataillade.alarm.services  NotificationCompat  com.gdelataillade.alarm.services  NotificationManager  com.gdelataillade.alarm.services  NotificationOnKillService  com.gdelataillade.alarm.services  
PendingIntent  com.gdelataillade.alarm.services  START_STICKY  com.gdelataillade.alarm.services  Settings  com.gdelataillade.alarm.services  String  com.gdelataillade.alarm.services  Suppress  com.gdelataillade.alarm.services  Timer  com.gdelataillade.alarm.services  Unit  com.gdelataillade.alarm.services  VibrationEffect  com.gdelataillade.alarm.services  VibrationService  com.gdelataillade.alarm.services  
VolumeService  com.gdelataillade.alarm.services  apply  com.gdelataillade.alarm.services  cancel  com.gdelataillade.alarm.services  
component1  com.gdelataillade.alarm.services  
component2  com.gdelataillade.alarm.services  context  com.gdelataillade.alarm.services  filter  com.gdelataillade.alarm.services  forEach  com.gdelataillade.alarm.services  let  com.gdelataillade.alarm.services  mediaPlayers  com.gdelataillade.alarm.services  onAudioComplete  com.gdelataillade.alarm.services  
plusAssign  com.gdelataillade.alarm.services  round  com.gdelataillade.alarm.services  set  com.gdelataillade.alarm.services  startFadeIn  com.gdelataillade.alarm.services  
startsWith  com.gdelataillade.alarm.services  timers  com.gdelataillade.alarm.services  toList  com.gdelataillade.alarm.services  Boolean -com.gdelataillade.alarm.services.AudioService  ConcurrentHashMap -com.gdelataillade.alarm.services.AudioService  Context -com.gdelataillade.alarm.services.AudioService  Double -com.gdelataillade.alarm.services.AudioService  	Exception -com.gdelataillade.alarm.services.AudioService  Int -com.gdelataillade.alarm.services.AudioService  List -com.gdelataillade.alarm.services.AudioService  Log -com.gdelataillade.alarm.services.AudioService  MediaPlayer -com.gdelataillade.alarm.services.AudioService  String -com.gdelataillade.alarm.services.AudioService  Timer -com.gdelataillade.alarm.services.AudioService  	TimerTask -com.gdelataillade.alarm.services.AudioService  Unit -com.gdelataillade.alarm.services.AudioService  apply -com.gdelataillade.alarm.services.AudioService  cancel -com.gdelataillade.alarm.services.AudioService  cleanUp -com.gdelataillade.alarm.services.AudioService  
component1 -com.gdelataillade.alarm.services.AudioService  
component2 -com.gdelataillade.alarm.services.AudioService  context -com.gdelataillade.alarm.services.AudioService  filter -com.gdelataillade.alarm.services.AudioService  getAPPLY -com.gdelataillade.alarm.services.AudioService  getApply -com.gdelataillade.alarm.services.AudioService  
getComponent1 -com.gdelataillade.alarm.services.AudioService  
getComponent2 -com.gdelataillade.alarm.services.AudioService  	getFILTER -com.gdelataillade.alarm.services.AudioService  	getFilter -com.gdelataillade.alarm.services.AudioService  
getPLUSAssign -com.gdelataillade.alarm.services.AudioService  getPlayingMediaPlayersIds -com.gdelataillade.alarm.services.AudioService  
getPlusAssign -com.gdelataillade.alarm.services.AudioService  getSET -com.gdelataillade.alarm.services.AudioService  
getSTARTSWith -com.gdelataillade.alarm.services.AudioService  getSet -com.gdelataillade.alarm.services.AudioService  
getStartsWith -com.gdelataillade.alarm.services.AudioService  	getTOList -com.gdelataillade.alarm.services.AudioService  	getToList -com.gdelataillade.alarm.services.AudioService  isMediaPlayerEmpty -com.gdelataillade.alarm.services.AudioService  mediaPlayers -com.gdelataillade.alarm.services.AudioService  onAudioComplete -com.gdelataillade.alarm.services.AudioService  	playAudio -com.gdelataillade.alarm.services.AudioService  
plusAssign -com.gdelataillade.alarm.services.AudioService  set -com.gdelataillade.alarm.services.AudioService  setOnAudioCompleteListener -com.gdelataillade.alarm.services.AudioService  startFadeIn -com.gdelataillade.alarm.services.AudioService  
startsWith -com.gdelataillade.alarm.services.AudioService  	stopAudio -com.gdelataillade.alarm.services.AudioService  timers -com.gdelataillade.alarm.services.AudioService  toList -com.gdelataillade.alarm.services.AudioService  
getPLUSAssign Lcom.gdelataillade.alarm.services.AudioService.startFadeIn.<no name provided>  
getPlusAssign Lcom.gdelataillade.alarm.services.AudioService.startFadeIn.<no name provided>  Build :com.gdelataillade.alarm.services.NotificationOnKillService  
CHANNEL_ID :com.gdelataillade.alarm.services.NotificationOnKillService  Context :com.gdelataillade.alarm.services.NotificationOnKillService  	Exception :com.gdelataillade.alarm.services.NotificationOnKillService  IBinder :com.gdelataillade.alarm.services.NotificationOnKillService  Int :com.gdelataillade.alarm.services.NotificationOnKillService  Intent :com.gdelataillade.alarm.services.NotificationOnKillService  Log :com.gdelataillade.alarm.services.NotificationOnKillService  NOTIFICATION_ID :com.gdelataillade.alarm.services.NotificationOnKillService  NotificationChannel :com.gdelataillade.alarm.services.NotificationOnKillService  NotificationCompat :com.gdelataillade.alarm.services.NotificationOnKillService  NotificationManager :com.gdelataillade.alarm.services.NotificationOnKillService  
PendingIntent :com.gdelataillade.alarm.services.NotificationOnKillService  RequiresApi :com.gdelataillade.alarm.services.NotificationOnKillService  START_STICKY :com.gdelataillade.alarm.services.NotificationOnKillService  Settings :com.gdelataillade.alarm.services.NotificationOnKillService  String :com.gdelataillade.alarm.services.NotificationOnKillService  apply :com.gdelataillade.alarm.services.NotificationOnKillService  body :com.gdelataillade.alarm.services.NotificationOnKillService  getAPPLY :com.gdelataillade.alarm.services.NotificationOnKillService  getApply :com.gdelataillade.alarm.services.NotificationOnKillService  getPACKAGEManager :com.gdelataillade.alarm.services.NotificationOnKillService  getPACKAGEName :com.gdelataillade.alarm.services.NotificationOnKillService  getPackageManager :com.gdelataillade.alarm.services.NotificationOnKillService  getPackageName :com.gdelataillade.alarm.services.NotificationOnKillService  getSystemService :com.gdelataillade.alarm.services.NotificationOnKillService  packageManager :com.gdelataillade.alarm.services.NotificationOnKillService  packageName :com.gdelataillade.alarm.services.NotificationOnKillService  setPackageManager :com.gdelataillade.alarm.services.NotificationOnKillService  setPackageName :com.gdelataillade.alarm.services.NotificationOnKillService  title :com.gdelataillade.alarm.services.NotificationOnKillService  Build 1com.gdelataillade.alarm.services.VibrationService  Context 1com.gdelataillade.alarm.services.VibrationService  Int 1com.gdelataillade.alarm.services.VibrationService  	LongArray 1com.gdelataillade.alarm.services.VibrationService  VibrationEffect 1com.gdelataillade.alarm.services.VibrationService  Vibrator 1com.gdelataillade.alarm.services.VibrationService  context 1com.gdelataillade.alarm.services.VibrationService  startVibrating 1com.gdelataillade.alarm.services.VibrationService  
stopVibrating 1com.gdelataillade.alarm.services.VibrationService  vibrator 1com.gdelataillade.alarm.services.VibrationService  AudioAttributes .com.gdelataillade.alarm.services.VolumeService  AudioFocusRequest .com.gdelataillade.alarm.services.VolumeService  AudioManager .com.gdelataillade.alarm.services.VolumeService  Boolean .com.gdelataillade.alarm.services.VolumeService  Build .com.gdelataillade.alarm.services.VolumeService  Context .com.gdelataillade.alarm.services.VolumeService  Double .com.gdelataillade.alarm.services.VolumeService  Int .com.gdelataillade.alarm.services.VolumeService  Log .com.gdelataillade.alarm.services.VolumeService  Suppress .com.gdelataillade.alarm.services.VolumeService  abandonAudioFocus .com.gdelataillade.alarm.services.VolumeService  audioManager .com.gdelataillade.alarm.services.VolumeService  context .com.gdelataillade.alarm.services.VolumeService  focusRequest .com.gdelataillade.alarm.services.VolumeService  getLET .com.gdelataillade.alarm.services.VolumeService  getLet .com.gdelataillade.alarm.services.VolumeService  getROUND .com.gdelataillade.alarm.services.VolumeService  getRound .com.gdelataillade.alarm.services.VolumeService  let .com.gdelataillade.alarm.services.VolumeService  previousVolume .com.gdelataillade.alarm.services.VolumeService  requestAudioFocus .com.gdelataillade.alarm.services.VolumeService  restorePreviousVolume .com.gdelataillade.alarm.services.VolumeService  round .com.gdelataillade.alarm.services.VolumeService  	setVolume .com.gdelataillade.alarm.services.VolumeService  Log 
io.flutter  d io.flutter.Log  e io.flutter.Log  
FlutterEngine io.flutter.embedding.engine  DartExecutor  io.flutter.embedding.engine.dart  
FlutterPlugin #io.flutter.embedding.engine.plugins  FlutterPluginBinding 1io.flutter.embedding.engine.plugins.FlutterPlugin  applicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  binaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getAPPLICATIONContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getApplicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getBINARYMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getBinaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  setApplicationContext Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  setBinaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  BinaryMessenger io.flutter.plugin.common  EventChannel io.flutter.plugin.common  
MethodCall io.flutter.plugin.common  
MethodChannel io.flutter.plugin.common  	EventSink %io.flutter.plugin.common.EventChannel  
StreamHandler %io.flutter.plugin.common.EventChannel  setStreamHandler %io.flutter.plugin.common.EventChannel  success /io.flutter.plugin.common.EventChannel.EventSink  argument #io.flutter.plugin.common.MethodCall  method #io.flutter.plugin.common.MethodCall  MethodCallHandler &io.flutter.plugin.common.MethodChannel  Result &io.flutter.plugin.common.MethodChannel  setMethodCallHandler &io.flutter.plugin.common.MethodChannel  error -io.flutter.plugin.common.MethodChannel.Result  notImplemented -io.flutter.plugin.common.MethodChannel.Result  success -io.flutter.plugin.common.MethodChannel.Result  FileDescriptor java.io  	getPARENT java.io.File  	getParent java.io.File  parent java.io.File  	setParent java.io.File  AlarmManager 	java.lang  AlarmPlugin 	java.lang  
AlarmReceiver 	java.lang  AlarmService 	java.lang  AudioAttributes 	java.lang  AudioFocusRequest 	java.lang  AudioManager 	java.lang  AudioService 	java.lang  Build 	java.lang  
CHANNEL_ID 	java.lang  CHANNEL_NAME 	java.lang  Class 	java.lang  ClassCastException 	java.lang  ConcurrentHashMap 	java.lang  Context 	java.lang  EventChannel 	java.lang  	Exception 	java.lang  Handler 	java.lang  IllegalStateException 	java.lang  Intent 	java.lang  Log 	java.lang  Looper 	java.lang  MediaPlayer 	java.lang  
MethodChannel 	java.lang  Notification 	java.lang  NotificationChannel 	java.lang  NotificationCompat 	java.lang  NotificationHandler 	java.lang  NotificationManager 	java.lang  NotificationOnKillService 	java.lang  
PendingIntent 	java.lang  PowerManager 	java.lang  START_NOT_STICKY 	java.lang  START_STICKY 	java.lang  SecurityException 	java.lang  ServiceInfo 	java.lang  Settings 	java.lang  System 	java.lang  Timer 	java.lang  VibrationEffect 	java.lang  VibrationService 	java.lang  
VolumeService 	java.lang  apply 	java.lang  cancel 	java.lang  
component1 	java.lang  
component2 	java.lang  contains 	java.lang  context 	java.lang  	eventSink 	java.lang  filter 	java.lang  forEach 	java.lang  java 	java.lang  let 	java.lang  listOf 	java.lang  longArrayOf 	java.lang  mapOf 	java.lang  mediaPlayers 	java.lang  onAudioComplete 	java.lang  
plusAssign 	java.lang  ringingAlarmIds 	java.lang  round 	java.lang  set 	java.lang  startFadeIn 	java.lang  
startsWith 	java.lang  timers 	java.lang  to 	java.lang  toList 	java.lang  message java.lang.Exception  printStackTrace java.lang.Exception  message java.lang.IllegalStateException  <SAM-CONSTRUCTOR> java.lang.Runnable  currentTimeMillis java.lang.System  Timer 	java.util  	TimerTask 	java.util  clear java.util.AbstractMap  filter java.util.AbstractMap  get java.util.AbstractMap  isEmpty java.util.AbstractMap  remove java.util.AbstractMap  set java.util.AbstractMap  cancel java.util.Timer  	getCANCEL java.util.Timer  	getCancel java.util.Timer  schedule java.util.Timer  cancel java.util.TimerTask  
plusAssign java.util.TimerTask  ConcurrentHashMap java.util.concurrent  clear &java.util.concurrent.ConcurrentHashMap  filter &java.util.concurrent.ConcurrentHashMap  get &java.util.concurrent.ConcurrentHashMap  	getFILTER &java.util.concurrent.ConcurrentHashMap  	getFilter &java.util.concurrent.ConcurrentHashMap  getSET &java.util.concurrent.ConcurrentHashMap  getSet &java.util.concurrent.ConcurrentHashMap  isEmpty &java.util.concurrent.ConcurrentHashMap  remove &java.util.concurrent.ConcurrentHashMap  set &java.util.concurrent.ConcurrentHashMap  values &java.util.concurrent.ConcurrentHashMap  AlarmManager kotlin  AlarmPlugin kotlin  
AlarmReceiver kotlin  AlarmService kotlin  Any kotlin  AudioAttributes kotlin  AudioFocusRequest kotlin  AudioManager kotlin  AudioService kotlin  Boolean kotlin  Build kotlin  
CHANNEL_ID kotlin  CHANNEL_NAME kotlin  ClassCastException kotlin  ConcurrentHashMap kotlin  Context kotlin  Double kotlin  EventChannel kotlin  	Exception kotlin  Float kotlin  	Function0 kotlin  	Function1 kotlin  Handler kotlin  IllegalStateException kotlin  Int kotlin  Intent kotlin  	JvmStatic kotlin  Log kotlin  Long kotlin  	LongArray kotlin  Looper kotlin  MediaPlayer kotlin  
MethodChannel kotlin  Nothing kotlin  Notification kotlin  NotificationChannel kotlin  NotificationCompat kotlin  NotificationHandler kotlin  NotificationManager kotlin  NotificationOnKillService kotlin  Pair kotlin  
PendingIntent kotlin  PowerManager kotlin  START_NOT_STICKY kotlin  START_STICKY kotlin  SecurityException kotlin  ServiceInfo kotlin  Settings kotlin  String kotlin  Suppress kotlin  System kotlin  Timer kotlin  Unit kotlin  VibrationEffect kotlin  VibrationService kotlin  
VolumeService kotlin  apply kotlin  cancel kotlin  
component1 kotlin  
component2 kotlin  contains kotlin  context kotlin  	eventSink kotlin  filter kotlin  forEach kotlin  java kotlin  let kotlin  listOf kotlin  longArrayOf kotlin  mapOf kotlin  mediaPlayers kotlin  onAudioComplete kotlin  
plusAssign kotlin  ringingAlarmIds kotlin  round kotlin  set kotlin  startFadeIn kotlin  
startsWith kotlin  timers kotlin  to kotlin  toList kotlin  
getPLUSAssign kotlin.Float  
getPlusAssign kotlin.Float  getLET 
kotlin.Int  getLet 
kotlin.Int  
getSTARTSWith 
kotlin.String  
getStartsWith 
kotlin.String  getTO 
kotlin.String  getTo 
kotlin.String  AlarmManager kotlin.annotation  AlarmPlugin kotlin.annotation  
AlarmReceiver kotlin.annotation  AlarmService kotlin.annotation  AudioAttributes kotlin.annotation  AudioFocusRequest kotlin.annotation  AudioManager kotlin.annotation  AudioService kotlin.annotation  Build kotlin.annotation  
CHANNEL_ID kotlin.annotation  CHANNEL_NAME kotlin.annotation  ClassCastException kotlin.annotation  ConcurrentHashMap kotlin.annotation  Context kotlin.annotation  EventChannel kotlin.annotation  	Exception kotlin.annotation  Handler kotlin.annotation  IllegalStateException kotlin.annotation  Intent kotlin.annotation  	JvmStatic kotlin.annotation  Log kotlin.annotation  Looper kotlin.annotation  MediaPlayer kotlin.annotation  
MethodChannel kotlin.annotation  Notification kotlin.annotation  NotificationChannel kotlin.annotation  NotificationCompat kotlin.annotation  NotificationHandler kotlin.annotation  NotificationManager kotlin.annotation  NotificationOnKillService kotlin.annotation  
PendingIntent kotlin.annotation  PowerManager kotlin.annotation  START_NOT_STICKY kotlin.annotation  START_STICKY kotlin.annotation  SecurityException kotlin.annotation  ServiceInfo kotlin.annotation  Settings kotlin.annotation  System kotlin.annotation  Timer kotlin.annotation  VibrationEffect kotlin.annotation  VibrationService kotlin.annotation  
VolumeService kotlin.annotation  apply kotlin.annotation  cancel kotlin.annotation  
component1 kotlin.annotation  
component2 kotlin.annotation  contains kotlin.annotation  context kotlin.annotation  	eventSink kotlin.annotation  filter kotlin.annotation  forEach kotlin.annotation  java kotlin.annotation  let kotlin.annotation  listOf kotlin.annotation  longArrayOf kotlin.annotation  mapOf kotlin.annotation  mediaPlayers kotlin.annotation  onAudioComplete kotlin.annotation  
plusAssign kotlin.annotation  ringingAlarmIds kotlin.annotation  round kotlin.annotation  set kotlin.annotation  startFadeIn kotlin.annotation  
startsWith kotlin.annotation  timers kotlin.annotation  to kotlin.annotation  toList kotlin.annotation  AlarmManager kotlin.collections  AlarmPlugin kotlin.collections  
AlarmReceiver kotlin.collections  AlarmService kotlin.collections  AudioAttributes kotlin.collections  AudioFocusRequest kotlin.collections  AudioManager kotlin.collections  AudioService kotlin.collections  Build kotlin.collections  
CHANNEL_ID kotlin.collections  CHANNEL_NAME kotlin.collections  ClassCastException kotlin.collections  ConcurrentHashMap kotlin.collections  Context kotlin.collections  EventChannel kotlin.collections  	Exception kotlin.collections  Handler kotlin.collections  IllegalStateException kotlin.collections  Intent kotlin.collections  	JvmStatic kotlin.collections  List kotlin.collections  Log kotlin.collections  Looper kotlin.collections  Map kotlin.collections  MediaPlayer kotlin.collections  
MethodChannel kotlin.collections  Notification kotlin.collections  NotificationChannel kotlin.collections  NotificationCompat kotlin.collections  NotificationHandler kotlin.collections  NotificationManager kotlin.collections  NotificationOnKillService kotlin.collections  
PendingIntent kotlin.collections  PowerManager kotlin.collections  START_NOT_STICKY kotlin.collections  START_STICKY kotlin.collections  SecurityException kotlin.collections  ServiceInfo kotlin.collections  Settings kotlin.collections  System kotlin.collections  Timer kotlin.collections  VibrationEffect kotlin.collections  VibrationService kotlin.collections  
VolumeService kotlin.collections  apply kotlin.collections  cancel kotlin.collections  
component1 kotlin.collections  
component2 kotlin.collections  contains kotlin.collections  context kotlin.collections  	eventSink kotlin.collections  filter kotlin.collections  forEach kotlin.collections  java kotlin.collections  let kotlin.collections  listOf kotlin.collections  longArrayOf kotlin.collections  mapOf kotlin.collections  mediaPlayers kotlin.collections  onAudioComplete kotlin.collections  
plusAssign kotlin.collections  ringingAlarmIds kotlin.collections  round kotlin.collections  set kotlin.collections  startFadeIn kotlin.collections  
startsWith kotlin.collections  timers kotlin.collections  to kotlin.collections  toList kotlin.collections  getCONTAINS kotlin.collections.List  getContains kotlin.collections.List  Entry kotlin.collections.Map  
getComponent1 kotlin.collections.Map.Entry  
getComponent2 kotlin.collections.Map.Entry  	getTOList kotlin.collections.Set  	getToList kotlin.collections.Set  AlarmManager kotlin.comparisons  AlarmPlugin kotlin.comparisons  
AlarmReceiver kotlin.comparisons  AlarmService kotlin.comparisons  AudioAttributes kotlin.comparisons  AudioFocusRequest kotlin.comparisons  AudioManager kotlin.comparisons  AudioService kotlin.comparisons  Build kotlin.comparisons  
CHANNEL_ID kotlin.comparisons  CHANNEL_NAME kotlin.comparisons  ClassCastException kotlin.comparisons  ConcurrentHashMap kotlin.comparisons  Context kotlin.comparisons  EventChannel kotlin.comparisons  	Exception kotlin.comparisons  Handler kotlin.comparisons  IllegalStateException kotlin.comparisons  Intent kotlin.comparisons  	JvmStatic kotlin.comparisons  Log kotlin.comparisons  Looper kotlin.comparisons  MediaPlayer kotlin.comparisons  
MethodChannel kotlin.comparisons  Notification kotlin.comparisons  NotificationChannel kotlin.comparisons  NotificationCompat kotlin.comparisons  NotificationHandler kotlin.comparisons  NotificationManager kotlin.comparisons  NotificationOnKillService kotlin.comparisons  
PendingIntent kotlin.comparisons  PowerManager kotlin.comparisons  START_NOT_STICKY kotlin.comparisons  START_STICKY kotlin.comparisons  SecurityException kotlin.comparisons  ServiceInfo kotlin.comparisons  Settings kotlin.comparisons  System kotlin.comparisons  Timer kotlin.comparisons  VibrationEffect kotlin.comparisons  VibrationService kotlin.comparisons  
VolumeService kotlin.comparisons  apply kotlin.comparisons  cancel kotlin.comparisons  
component1 kotlin.comparisons  
component2 kotlin.comparisons  contains kotlin.comparisons  context kotlin.comparisons  	eventSink kotlin.comparisons  filter kotlin.comparisons  forEach kotlin.comparisons  java kotlin.comparisons  let kotlin.comparisons  listOf kotlin.comparisons  longArrayOf kotlin.comparisons  mapOf kotlin.comparisons  mediaPlayers kotlin.comparisons  onAudioComplete kotlin.comparisons  
plusAssign kotlin.comparisons  ringingAlarmIds kotlin.comparisons  round kotlin.comparisons  set kotlin.comparisons  startFadeIn kotlin.comparisons  
startsWith kotlin.comparisons  timers kotlin.comparisons  to kotlin.comparisons  toList kotlin.comparisons  AlarmManager 	kotlin.io  AlarmPlugin 	kotlin.io  
AlarmReceiver 	kotlin.io  AlarmService 	kotlin.io  AudioAttributes 	kotlin.io  AudioFocusRequest 	kotlin.io  AudioManager 	kotlin.io  AudioService 	kotlin.io  Build 	kotlin.io  
CHANNEL_ID 	kotlin.io  CHANNEL_NAME 	kotlin.io  ClassCastException 	kotlin.io  ConcurrentHashMap 	kotlin.io  Context 	kotlin.io  EventChannel 	kotlin.io  	Exception 	kotlin.io  Handler 	kotlin.io  IllegalStateException 	kotlin.io  Intent 	kotlin.io  	JvmStatic 	kotlin.io  Log 	kotlin.io  Looper 	kotlin.io  MediaPlayer 	kotlin.io  
MethodChannel 	kotlin.io  Notification 	kotlin.io  NotificationChannel 	kotlin.io  NotificationCompat 	kotlin.io  NotificationHandler 	kotlin.io  NotificationManager 	kotlin.io  NotificationOnKillService 	kotlin.io  
PendingIntent 	kotlin.io  PowerManager 	kotlin.io  START_NOT_STICKY 	kotlin.io  START_STICKY 	kotlin.io  SecurityException 	kotlin.io  ServiceInfo 	kotlin.io  Settings 	kotlin.io  System 	kotlin.io  Timer 	kotlin.io  VibrationEffect 	kotlin.io  VibrationService 	kotlin.io  
VolumeService 	kotlin.io  apply 	kotlin.io  cancel 	kotlin.io  
component1 	kotlin.io  
component2 	kotlin.io  contains 	kotlin.io  context 	kotlin.io  	eventSink 	kotlin.io  filter 	kotlin.io  forEach 	kotlin.io  java 	kotlin.io  let 	kotlin.io  listOf 	kotlin.io  longArrayOf 	kotlin.io  mapOf 	kotlin.io  mediaPlayers 	kotlin.io  onAudioComplete 	kotlin.io  
plusAssign 	kotlin.io  ringingAlarmIds 	kotlin.io  round 	kotlin.io  set 	kotlin.io  startFadeIn 	kotlin.io  
startsWith 	kotlin.io  timers 	kotlin.io  to 	kotlin.io  toList 	kotlin.io  AlarmManager 
kotlin.jvm  AlarmPlugin 
kotlin.jvm  
AlarmReceiver 
kotlin.jvm  AlarmService 
kotlin.jvm  AudioAttributes 
kotlin.jvm  AudioFocusRequest 
kotlin.jvm  AudioManager 
kotlin.jvm  AudioService 
kotlin.jvm  Build 
kotlin.jvm  
CHANNEL_ID 
kotlin.jvm  CHANNEL_NAME 
kotlin.jvm  ClassCastException 
kotlin.jvm  ConcurrentHashMap 
kotlin.jvm  Context 
kotlin.jvm  EventChannel 
kotlin.jvm  	Exception 
kotlin.jvm  Handler 
kotlin.jvm  IllegalStateException 
kotlin.jvm  Intent 
kotlin.jvm  	JvmStatic 
kotlin.jvm  Log 
kotlin.jvm  Looper 
kotlin.jvm  MediaPlayer 
kotlin.jvm  
MethodChannel 
kotlin.jvm  Notification 
kotlin.jvm  NotificationChannel 
kotlin.jvm  NotificationCompat 
kotlin.jvm  NotificationHandler 
kotlin.jvm  NotificationManager 
kotlin.jvm  NotificationOnKillService 
kotlin.jvm  
PendingIntent 
kotlin.jvm  PowerManager 
kotlin.jvm  START_NOT_STICKY 
kotlin.jvm  START_STICKY 
kotlin.jvm  SecurityException 
kotlin.jvm  ServiceInfo 
kotlin.jvm  Settings 
kotlin.jvm  System 
kotlin.jvm  Timer 
kotlin.jvm  VibrationEffect 
kotlin.jvm  VibrationService 
kotlin.jvm  
VolumeService 
kotlin.jvm  apply 
kotlin.jvm  cancel 
kotlin.jvm  
component1 
kotlin.jvm  
component2 
kotlin.jvm  contains 
kotlin.jvm  context 
kotlin.jvm  	eventSink 
kotlin.jvm  filter 
kotlin.jvm  forEach 
kotlin.jvm  java 
kotlin.jvm  let 
kotlin.jvm  listOf 
kotlin.jvm  longArrayOf 
kotlin.jvm  mapOf 
kotlin.jvm  mediaPlayers 
kotlin.jvm  onAudioComplete 
kotlin.jvm  
plusAssign 
kotlin.jvm  ringingAlarmIds 
kotlin.jvm  round 
kotlin.jvm  set 
kotlin.jvm  startFadeIn 
kotlin.jvm  
startsWith 
kotlin.jvm  timers 
kotlin.jvm  to 
kotlin.jvm  toList 
kotlin.jvm  round kotlin.math  AlarmManager 
kotlin.ranges  AlarmPlugin 
kotlin.ranges  
AlarmReceiver 
kotlin.ranges  AlarmService 
kotlin.ranges  AudioAttributes 
kotlin.ranges  AudioFocusRequest 
kotlin.ranges  AudioManager 
kotlin.ranges  AudioService 
kotlin.ranges  Build 
kotlin.ranges  
CHANNEL_ID 
kotlin.ranges  CHANNEL_NAME 
kotlin.ranges  ClassCastException 
kotlin.ranges  ConcurrentHashMap 
kotlin.ranges  Context 
kotlin.ranges  EventChannel 
kotlin.ranges  	Exception 
kotlin.ranges  Handler 
kotlin.ranges  IllegalStateException 
kotlin.ranges  Intent 
kotlin.ranges  	JvmStatic 
kotlin.ranges  Log 
kotlin.ranges  Looper 
kotlin.ranges  MediaPlayer 
kotlin.ranges  
MethodChannel 
kotlin.ranges  Notification 
kotlin.ranges  NotificationChannel 
kotlin.ranges  NotificationCompat 
kotlin.ranges  NotificationHandler 
kotlin.ranges  NotificationManager 
kotlin.ranges  NotificationOnKillService 
kotlin.ranges  
PendingIntent 
kotlin.ranges  PowerManager 
kotlin.ranges  START_NOT_STICKY 
kotlin.ranges  START_STICKY 
kotlin.ranges  SecurityException 
kotlin.ranges  ServiceInfo 
kotlin.ranges  Settings 
kotlin.ranges  System 
kotlin.ranges  Timer 
kotlin.ranges  VibrationEffect 
kotlin.ranges  VibrationService 
kotlin.ranges  
VolumeService 
kotlin.ranges  apply 
kotlin.ranges  cancel 
kotlin.ranges  
component1 
kotlin.ranges  
component2 
kotlin.ranges  contains 
kotlin.ranges  context 
kotlin.ranges  	eventSink 
kotlin.ranges  filter 
kotlin.ranges  forEach 
kotlin.ranges  java 
kotlin.ranges  let 
kotlin.ranges  listOf 
kotlin.ranges  longArrayOf 
kotlin.ranges  mapOf 
kotlin.ranges  mediaPlayers 
kotlin.ranges  onAudioComplete 
kotlin.ranges  
plusAssign 
kotlin.ranges  ringingAlarmIds 
kotlin.ranges  round 
kotlin.ranges  set 
kotlin.ranges  startFadeIn 
kotlin.ranges  
startsWith 
kotlin.ranges  timers 
kotlin.ranges  to 
kotlin.ranges  toList 
kotlin.ranges  KClass kotlin.reflect  getJAVA kotlin.reflect.KClass  getJava kotlin.reflect.KClass  java kotlin.reflect.KClass  AlarmManager kotlin.sequences  AlarmPlugin kotlin.sequences  
AlarmReceiver kotlin.sequences  AlarmService kotlin.sequences  AudioAttributes kotlin.sequences  AudioFocusRequest kotlin.sequences  AudioManager kotlin.sequences  AudioService kotlin.sequences  Build kotlin.sequences  
CHANNEL_ID kotlin.sequences  CHANNEL_NAME kotlin.sequences  ClassCastException kotlin.sequences  ConcurrentHashMap kotlin.sequences  Context kotlin.sequences  EventChannel kotlin.sequences  	Exception kotlin.sequences  Handler kotlin.sequences  IllegalStateException kotlin.sequences  Intent kotlin.sequences  	JvmStatic kotlin.sequences  Log kotlin.sequences  Looper kotlin.sequences  MediaPlayer kotlin.sequences  
MethodChannel kotlin.sequences  Notification kotlin.sequences  NotificationChannel kotlin.sequences  NotificationCompat kotlin.sequences  NotificationHandler kotlin.sequences  NotificationManager kotlin.sequences  NotificationOnKillService kotlin.sequences  
PendingIntent kotlin.sequences  PowerManager kotlin.sequences  START_NOT_STICKY kotlin.sequences  START_STICKY kotlin.sequences  SecurityException kotlin.sequences  ServiceInfo kotlin.sequences  Settings kotlin.sequences  System kotlin.sequences  Timer kotlin.sequences  VibrationEffect kotlin.sequences  VibrationService kotlin.sequences  
VolumeService kotlin.sequences  apply kotlin.sequences  cancel kotlin.sequences  
component1 kotlin.sequences  
component2 kotlin.sequences  contains kotlin.sequences  context kotlin.sequences  	eventSink kotlin.sequences  filter kotlin.sequences  forEach kotlin.sequences  java kotlin.sequences  let kotlin.sequences  listOf kotlin.sequences  longArrayOf kotlin.sequences  mapOf kotlin.sequences  mediaPlayers kotlin.sequences  onAudioComplete kotlin.sequences  
plusAssign kotlin.sequences  ringingAlarmIds kotlin.sequences  round kotlin.sequences  set kotlin.sequences  startFadeIn kotlin.sequences  
startsWith kotlin.sequences  timers kotlin.sequences  to kotlin.sequences  toList kotlin.sequences  AlarmManager kotlin.text  AlarmPlugin kotlin.text  
AlarmReceiver kotlin.text  AlarmService kotlin.text  AudioAttributes kotlin.text  AudioFocusRequest kotlin.text  AudioManager kotlin.text  AudioService kotlin.text  Build kotlin.text  
CHANNEL_ID kotlin.text  CHANNEL_NAME kotlin.text  ClassCastException kotlin.text  ConcurrentHashMap kotlin.text  Context kotlin.text  EventChannel kotlin.text  	Exception kotlin.text  Handler kotlin.text  IllegalStateException kotlin.text  Intent kotlin.text  	JvmStatic kotlin.text  Log kotlin.text  Looper kotlin.text  MediaPlayer kotlin.text  
MethodChannel kotlin.text  Notification kotlin.text  NotificationChannel kotlin.text  NotificationCompat kotlin.text  NotificationHandler kotlin.text  NotificationManager kotlin.text  NotificationOnKillService kotlin.text  
PendingIntent kotlin.text  PowerManager kotlin.text  START_NOT_STICKY kotlin.text  START_STICKY kotlin.text  SecurityException kotlin.text  ServiceInfo kotlin.text  Settings kotlin.text  System kotlin.text  Timer kotlin.text  VibrationEffect kotlin.text  VibrationService kotlin.text  
VolumeService kotlin.text  apply kotlin.text  cancel kotlin.text  
component1 kotlin.text  
component2 kotlin.text  contains kotlin.text  context kotlin.text  	eventSink kotlin.text  filter kotlin.text  forEach kotlin.text  java kotlin.text  let kotlin.text  listOf kotlin.text  longArrayOf kotlin.text  mapOf kotlin.text  mediaPlayers kotlin.text  onAudioComplete kotlin.text  
plusAssign kotlin.text  ringingAlarmIds kotlin.text  round kotlin.text  set kotlin.text  startFadeIn kotlin.text  
startsWith kotlin.text  timers kotlin.text  to kotlin.text  toList kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               