1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.lekky.app"
4    android:versionCode="7"
5    android:versionName="1.0.4" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->D:\000.Workspace\Lekky\android\app\src\debug\AndroidManifest.xml:6:5-66
15-->D:\000.Workspace\Lekky\android\app\src\debug\AndroidManifest.xml:6:22-64
16    <!-- Add permissions for notifications -->
17    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> <!-- Add permission for exact alarms (Android 12+ / API 31+) -->
17-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:4:5-76
17-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:4:22-74
18    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" /> <!-- Add permission for boot completed to reschedule notifications -->
18-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:6:5-78
18-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:6:22-76
19    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" /> <!-- Add permissions for background work -->
19-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:8:5-80
19-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:8:22-78
20    <uses-permission android:name="android.permission.WAKE_LOCK" />
20-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:10:5-67
20-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:10:22-65
21    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
21-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:11:5-76
21-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:11:22-74
22    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" /> <!-- Add permission to ignore battery optimizations -->
22-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:12:5-86
22-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:12:22-84
23    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" /> <!-- Add permission for alarm clock notifications -->
23-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:14:5-94
23-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:14:22-92
24    <uses-permission android:name="android.permission.USE_EXACT_ALARM" /> <!-- Add permissions for alarm package -->
24-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:16:5-73
24-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:16:22-71
25    <uses-permission android:name="android.permission.VIBRATE" />
25-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:18:5-65
25-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:18:22-63
26    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />
26-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:19:5-80
26-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:19:22-78
27    <uses-permission android:name="android.permission.ACCESS_NOTIFICATION_POLICY" />
27-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:20:5-84
27-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:20:22-82
28    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" /> <!-- Storage permissions for file export -->
28-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:21:5-91
28-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:21:22-89
29    <uses-permission
29-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:24:5-26:38
30        android:name="android.permission.READ_EXTERNAL_STORAGE"
30-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:25:9-64
31        android:maxSdkVersion="32" />
31-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:26:9-35
32    <uses-permission
32-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:27:5-29:38
33        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
33-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:28:9-65
34        android:maxSdkVersion="29" /> <!-- Android 13+ (API 33+) granular media permissions -->
34-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:29:9-35
35    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
35-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:32:5-76
35-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:32:22-73
36    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
36-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:33:5-75
36-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:33:22-72
37    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" /> <!-- Android 14+ (API 34+) partial media access -->
37-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:34:5-75
37-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:34:22-72
38    <uses-permission android:name="android.permission.READ_MEDIA_VISUAL_USER_SELECTED" />
38-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:37:5-90
38-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:37:22-87
39    <!--
40 Required to query activities that can process text, see:
41         https://developer.android.com/training/package-visibility?hl=en and
42         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
43
44         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
45    -->
46    <queries>
46-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:111:5-128:15
47        <intent>
47-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:112:9-115:18
48            <action android:name="android.intent.action.PROCESS_TEXT" />
48-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:113:13-72
48-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:113:21-70
49
50            <data android:mimeType="text/plain" />
50-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:114:13-50
50-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:114:19-48
51        </intent>
52        <!-- For document selection -->
53        <intent>
53-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:117:9-119:18
54            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
54-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:118:13-79
54-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:118:21-76
55        </intent>
56        <intent>
56-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:120:9-123:18
57            <action android:name="android.intent.action.CREATE_DOCUMENT" />
57-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:121:13-76
57-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:121:21-73
58
59            <data android:mimeType="text/csv" />
59-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:114:13-50
59-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:114:19-48
60        </intent>
61        <intent>
61-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:124:9-127:18
62            <action android:name="android.intent.action.OPEN_DOCUMENT" />
62-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:125:13-74
62-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:125:21-71
63
64            <data android:mimeType="text/csv" />
64-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:114:13-50
64-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:114:19-48
65        </intent>
66        <intent>
66-->[:file_picker] D:\000.Workspace\Lekky\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:9-14:18
67            <action android:name="android.intent.action.GET_CONTENT" />
67-->[:file_picker] D:\000.Workspace\Lekky\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-72
67-->[:file_picker] D:\000.Workspace\Lekky\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:21-69
68
69            <data android:mimeType="*/*" />
69-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:114:13-50
69-->D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:114:19-48
70        </intent>
71    </queries>
72
73    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
73-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:24:5-79
73-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:24:22-76
74
75    <permission
75-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-4\6e1dc6c49b9b4e7630c6b57963983704\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
76        android:name="com.lekky.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
76-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-4\6e1dc6c49b9b4e7630c6b57963983704\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
77        android:protectionLevel="signature" />
77-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-4\6e1dc6c49b9b4e7630c6b57963983704\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
78
79    <uses-permission android:name="com.lekky.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
79-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-4\6e1dc6c49b9b4e7630c6b57963983704\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
79-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-4\6e1dc6c49b9b4e7630c6b57963983704\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
80    <!--
81 Note: For Android 10+ (API 29+), we use Storage Access Framework (SAF)
82         which doesn't require additional permissions for user-selected locations
83    -->
84    <application
85        android:name="android.app.Application"
86        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
86-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-4\6e1dc6c49b9b4e7630c6b57963983704\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
87        android:debuggable="true"
88        android:enableOnBackInvokedCallback="true"
89        android:extractNativeLibs="true"
90        android:icon="@mipmap/ic_launcher"
91        android:label="lekky"
92        android:requestLegacyExternalStorage="true" >
93        <activity
94            android:name="com.lekky.app.MainActivity"
95            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
96            android:exported="true"
97            android:hardwareAccelerated="true"
98            android:launchMode="singleTop"
99            android:theme="@style/LaunchTheme"
100            android:windowSoftInputMode="adjustResize" >
101
102            <!--
103                 Specifies an Android theme to apply to this Activity as soon as
104                 the Android process has started. This theme is visible to the user
105                 while the Flutter UI initializes. After that, this theme continues
106                 to determine the Window background behind the Flutter UI.
107            -->
108            <meta-data
109                android:name="io.flutter.embedding.android.NormalTheme"
110                android:resource="@style/NormalTheme" />
111
112            <intent-filter>
113                <action android:name="android.intent.action.MAIN" />
114
115                <category android:name="android.intent.category.LAUNCHER" />
116            </intent-filter>
117        </activity>
118        <!--
119             Don't delete the meta-data below.
120             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
121        -->
122        <meta-data
123            android:name="flutterEmbedding"
124            android:value="2" />
125
126        <!-- Add receiver for boot completed to reschedule notifications -->
127        <receiver
128            android:name="com.dexterous.flutterlocalnotifications.ScheduledNotificationBootReceiver"
129            android:exported="true" >
130            <intent-filter>
131                <action android:name="android.intent.action.BOOT_COMPLETED" />
131-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:17-79
131-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:25-76
132                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
133            </intent-filter>
134        </receiver>
135
136        <!-- Foreground service for reliable notifications -->
137        <service
138            android:name="com.lekky.app.NotificationForegroundService"
139            android:enabled="true"
140            android:exported="false"
141            android:foregroundServiceType="dataSync" />
142
143        <!-- Alarm package receiver and service -->
144        <receiver android:name="com.gdelataillade.alarm.alarm.AlarmReceiver" />
145
146        <service
147            android:name="com.gdelataillade.alarm.alarm.AlarmService"
148            android:exported="false"
149            android:foregroundServiceType="mediaPlayback" />
150
151        <!-- Package name for the application -->
152        <meta-data
153            android:name="com.lekky.app.PACKAGE_NAME"
154            android:value="com.lekky.app" />
155
156        <activity
156-->[:url_launcher_android] D:\000.Workspace\Lekky\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
157            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
157-->[:url_launcher_android] D:\000.Workspace\Lekky\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
158            android:exported="false"
158-->[:url_launcher_android] D:\000.Workspace\Lekky\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
159            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
159-->[:url_launcher_android] D:\000.Workspace\Lekky\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
160
161        <uses-library
161-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\76e7c0e46e1e41ca65da1405b9eb1cd5\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
162            android:name="androidx.window.extensions"
162-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\76e7c0e46e1e41ca65da1405b9eb1cd5\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
163            android:required="false" />
163-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\76e7c0e46e1e41ca65da1405b9eb1cd5\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
164        <uses-library
164-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\76e7c0e46e1e41ca65da1405b9eb1cd5\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
165            android:name="androidx.window.sidecar"
165-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\76e7c0e46e1e41ca65da1405b9eb1cd5\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
166            android:required="false" />
166-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\76e7c0e46e1e41ca65da1405b9eb1cd5\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
167
168        <meta-data
168-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\fa41a8f4d43ebdb1a2bdc7a1d2c6fe82\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
169            android:name="com.google.android.gms.version"
169-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\fa41a8f4d43ebdb1a2bdc7a1d2c6fe82\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
170            android:value="@integer/google_play_services_version" />
170-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\fa41a8f4d43ebdb1a2bdc7a1d2c6fe82\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
171
172        <provider
172-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:29:9-37:20
173            android:name="androidx.startup.InitializationProvider"
173-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:30:13-67
174            android:authorities="com.lekky.app.androidx-startup"
174-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:31:13-68
175            android:exported="false" >
175-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:32:13-37
176            <meta-data
176-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
177                android:name="androidx.work.WorkManagerInitializer"
177-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:35:17-68
178                android:value="androidx.startup" />
178-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:36:17-49
179            <meta-data
179-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\63063b26a8d5be2fb0fed04707a2e278\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
180                android:name="androidx.emoji2.text.EmojiCompatInitializer"
180-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\63063b26a8d5be2fb0fed04707a2e278\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
181                android:value="androidx.startup" />
181-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\63063b26a8d5be2fb0fed04707a2e278\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
182            <meta-data
182-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\1758dd11e3be0aa50b0e6559e83f82a9\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
183                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
183-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\1758dd11e3be0aa50b0e6559e83f82a9\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78
184                android:value="androidx.startup" />
184-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\1758dd11e3be0aa50b0e6559e83f82a9\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
185        </provider>
186
187        <service
187-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
188            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
188-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
189            android:directBootAware="false"
189-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
190            android:enabled="@bool/enable_system_alarm_service_default"
190-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
191            android:exported="false" />
191-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
192        <service
192-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
193            android:name="androidx.work.impl.background.systemjob.SystemJobService"
193-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
194            android:directBootAware="false"
194-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
195            android:enabled="@bool/enable_system_job_service_default"
195-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
196            android:exported="true"
196-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
197            android:permission="android.permission.BIND_JOB_SERVICE" />
197-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
198        <service
198-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
199            android:name="androidx.work.impl.foreground.SystemForegroundService"
199-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
200            android:directBootAware="false"
200-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
201            android:enabled="@bool/enable_system_foreground_service_default"
201-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
202            android:exported="false" />
202-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
203
204        <receiver
204-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
205            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
205-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
206            android:directBootAware="false"
206-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
207            android:enabled="true"
207-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
208            android:exported="false" />
208-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
209        <receiver
209-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
210            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
210-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
211            android:directBootAware="false"
211-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
212            android:enabled="false"
212-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
213            android:exported="false" >
213-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
214            <intent-filter>
214-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
215                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
215-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
215-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
216                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
216-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
216-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
217            </intent-filter>
218        </receiver>
219        <receiver
219-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
220            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
220-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
221            android:directBootAware="false"
221-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
222            android:enabled="false"
222-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
223            android:exported="false" >
223-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
224            <intent-filter>
224-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
225                <action android:name="android.intent.action.BATTERY_OKAY" />
225-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
225-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
226                <action android:name="android.intent.action.BATTERY_LOW" />
226-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
226-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
227            </intent-filter>
228        </receiver>
229        <receiver
229-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
230            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
230-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
231            android:directBootAware="false"
231-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
232            android:enabled="false"
232-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
233            android:exported="false" >
233-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
234            <intent-filter>
234-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
235                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
235-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
235-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
236                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
236-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
236-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
237            </intent-filter>
238        </receiver>
239        <receiver
239-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
240            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
240-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
241            android:directBootAware="false"
241-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
242            android:enabled="false"
242-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
243            android:exported="false" >
243-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
244            <intent-filter>
244-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
245                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
245-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
245-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
246            </intent-filter>
247        </receiver>
248        <receiver
248-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
249            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
249-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
250            android:directBootAware="false"
250-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
251            android:enabled="false"
251-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
252            android:exported="false" >
252-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
253            <intent-filter>
253-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
254                <action android:name="android.intent.action.BOOT_COMPLETED" />
254-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:17-79
254-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:25-76
255                <action android:name="android.intent.action.TIME_SET" />
255-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
255-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
256                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
256-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
256-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
257            </intent-filter>
258        </receiver>
259        <receiver
259-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
260            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
260-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
261            android:directBootAware="false"
261-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
262            android:enabled="@bool/enable_system_alarm_service_default"
262-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
263            android:exported="false" >
263-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
264            <intent-filter>
264-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
265                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
265-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
265-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
266            </intent-filter>
267        </receiver>
268        <receiver
268-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
269            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
269-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
270            android:directBootAware="false"
270-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
271            android:enabled="true"
271-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
272            android:exported="true"
272-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
273            android:permission="android.permission.DUMP" >
273-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
274            <intent-filter>
274-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
275                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
275-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
275-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
276            </intent-filter>
277        </receiver>
278
279        <service
279-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\ffce1bb1ba7d6308e78ae0ff3994d469\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
280            android:name="androidx.room.MultiInstanceInvalidationService"
280-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\ffce1bb1ba7d6308e78ae0ff3994d469\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
281            android:directBootAware="true"
281-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\ffce1bb1ba7d6308e78ae0ff3994d469\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
282            android:exported="false" /> <!-- The activities will be merged into the manifest of the hosting app. -->
282-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\ffce1bb1ba7d6308e78ae0ff3994d469\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
283        <activity
283-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\ce88c44a4a968999ee4b64dbbb01246d\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:14:9-18:65
284            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
284-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\ce88c44a4a968999ee4b64dbbb01246d\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:15:13-93
285            android:exported="false"
285-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\ce88c44a4a968999ee4b64dbbb01246d\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:16:13-37
286            android:stateNotNeeded="true"
286-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\ce88c44a4a968999ee4b64dbbb01246d\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:17:13-42
287            android:theme="@style/Theme.PlayCore.Transparent" />
287-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\ce88c44a4a968999ee4b64dbbb01246d\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:18:13-62
288    </application>
289
290</manifest>
