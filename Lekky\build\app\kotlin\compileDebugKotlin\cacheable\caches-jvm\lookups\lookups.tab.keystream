  R android  color 	android.R  transparent android.R.color  ActivityManager android.app  Build android.app  
CHANNEL_ID android.app  Context android.app  Intent android.app  Log android.app  MainActivity android.app  NOTIFICATION_ID android.app  Notification android.app  NotificationChannel android.app  NotificationCompat android.app  NotificationForegroundService android.app  NotificationManager android.app  
PendingIntent android.app  START_STICKY android.app  Service android.app  TAG android.app  android android.app  apply android.app  java android.app  ActivityManager android.app.Activity  Boolean android.app.Activity  Class android.app.Activity  Context android.app.Activity  	Exception android.app.Activity  
FlutterEngine android.app.Activity  Integer android.app.Activity  
MethodChannel android.app.Activity  NotificationForegroundService android.app.Activity  configureFlutterEngine android.app.Activity  getSystemService android.app.Activity  isServiceRunning android.app.Activity  java android.app.Activity  RunningServiceInfo android.app.ActivityManager  getRunningServices android.app.ActivityManager  service .android.app.ActivityManager.RunningServiceInfo  VISIBILITY_SECRET android.app.Notification  Notification android.app.NotificationChannel  apply android.app.NotificationChannel  description android.app.NotificationChannel  enableLights android.app.NotificationChannel  enableVibration android.app.NotificationChannel  getAPPLY android.app.NotificationChannel  getApply android.app.NotificationChannel  getDESCRIPTION android.app.NotificationChannel  getDescription android.app.NotificationChannel  getLOCKSCREENVisibility android.app.NotificationChannel  getLockscreenVisibility android.app.NotificationChannel  lockscreenVisibility android.app.NotificationChannel  setDescription android.app.NotificationChannel  setLockscreenVisibility android.app.NotificationChannel  setShowBadge android.app.NotificationChannel  setSound android.app.NotificationChannel  IMPORTANCE_MIN android.app.NotificationManager  createNotificationChannel android.app.NotificationManager  FLAG_IMMUTABLE android.app.PendingIntent  FLAG_UPDATE_CURRENT android.app.PendingIntent  getActivity android.app.PendingIntent  Build android.app.Service  
CHANNEL_ID android.app.Service  Context android.app.Service  IBinder android.app.Service  Int android.app.Service  Intent android.app.Service  Log android.app.Service  MainActivity android.app.Service  NOTIFICATION_ID android.app.Service  Notification android.app.Service  NotificationChannel android.app.Service  NotificationCompat android.app.Service  NotificationForegroundService android.app.Service  NotificationManager android.app.Service  
PendingIntent android.app.Service  START_STICKY android.app.Service  TAG android.app.Service  android android.app.Service  apply android.app.Service  createNotification android.app.Service  createNotificationChannel android.app.Service  getSystemService android.app.Service  java android.app.Service  onCreate android.app.Service  	onDestroy android.app.Service  startForeground android.app.Service  
ComponentName android.content  Context android.content  Intent android.content  	className android.content.ComponentName  getCLASSName android.content.ComponentName  getClassName android.content.ComponentName  setClassName android.content.ComponentName  ACTIVITY_SERVICE android.content.Context  ActivityManager android.content.Context  Boolean android.content.Context  Build android.content.Context  
CHANNEL_ID android.content.Context  Class android.content.Context  Context android.content.Context  	Exception android.content.Context  
FlutterEngine android.content.Context  IBinder android.content.Context  Int android.content.Context  Integer android.content.Context  Intent android.content.Context  Log android.content.Context  MainActivity android.content.Context  
MethodChannel android.content.Context  NOTIFICATION_ID android.content.Context  NOTIFICATION_SERVICE android.content.Context  Notification android.content.Context  NotificationChannel android.content.Context  NotificationCompat android.content.Context  NotificationForegroundService android.content.Context  NotificationManager android.content.Context  
PendingIntent android.content.Context  START_STICKY android.content.Context  TAG android.content.Context  android android.content.Context  apply android.content.Context  configureFlutterEngine android.content.Context  createNotification android.content.Context  createNotificationChannel android.content.Context  getSystemService android.content.Context  isServiceRunning android.content.Context  java android.content.Context  onCreate android.content.Context  	onDestroy android.content.Context  startForeground android.content.Context  startForegroundService android.content.Context  startService android.content.Context  stopService android.content.Context  ActivityManager android.content.ContextWrapper  Boolean android.content.ContextWrapper  Build android.content.ContextWrapper  
CHANNEL_ID android.content.ContextWrapper  Class android.content.ContextWrapper  Context android.content.ContextWrapper  	Exception android.content.ContextWrapper  
FlutterEngine android.content.ContextWrapper  IBinder android.content.ContextWrapper  Int android.content.ContextWrapper  Integer android.content.ContextWrapper  Intent android.content.ContextWrapper  Log android.content.ContextWrapper  MainActivity android.content.ContextWrapper  
MethodChannel android.content.ContextWrapper  NOTIFICATION_ID android.content.ContextWrapper  Notification android.content.ContextWrapper  NotificationChannel android.content.ContextWrapper  NotificationCompat android.content.ContextWrapper  NotificationForegroundService android.content.ContextWrapper  NotificationManager android.content.ContextWrapper  
PendingIntent android.content.ContextWrapper  START_STICKY android.content.ContextWrapper  TAG android.content.ContextWrapper  android android.content.ContextWrapper  apply android.content.ContextWrapper  configureFlutterEngine android.content.ContextWrapper  createNotification android.content.ContextWrapper  createNotificationChannel android.content.ContextWrapper  getSystemService android.content.ContextWrapper  isServiceRunning android.content.ContextWrapper  java android.content.ContextWrapper  onCreate android.content.ContextWrapper  	onDestroy android.content.ContextWrapper  startForeground android.content.ContextWrapper  FLAG_ACTIVITY_CLEAR_TASK android.content.Intent  FLAG_ACTIVITY_NEW_TASK android.content.Intent  Intent android.content.Intent  apply android.content.Intent  flags android.content.Intent  getAPPLY android.content.Intent  getApply android.content.Intent  getFLAGS android.content.Intent  getFlags android.content.Intent  setFlags android.content.Intent  Build 
android.os  IBinder 
android.os  VERSION android.os.Build  
VERSION_CODES android.os.Build  SDK_INT android.os.Build.VERSION  M android.os.Build.VERSION_CODES  O android.os.Build.VERSION_CODES  ActivityManager  android.view.ContextThemeWrapper  Boolean  android.view.ContextThemeWrapper  Class  android.view.ContextThemeWrapper  Context  android.view.ContextThemeWrapper  	Exception  android.view.ContextThemeWrapper  
FlutterEngine  android.view.ContextThemeWrapper  Integer  android.view.ContextThemeWrapper  
MethodChannel  android.view.ContextThemeWrapper  NotificationForegroundService  android.view.ContextThemeWrapper  configureFlutterEngine  android.view.ContextThemeWrapper  getSystemService  android.view.ContextThemeWrapper  isServiceRunning  android.view.ContextThemeWrapper  java  android.view.ContextThemeWrapper  NotificationCompat androidx.core.app  Builder $androidx.core.app.NotificationCompat  CATEGORY_SERVICE $androidx.core.app.NotificationCompat  PRIORITY_MIN $androidx.core.app.NotificationCompat  VISIBILITY_SECRET $androidx.core.app.NotificationCompat  build ,androidx.core.app.NotificationCompat.Builder  setCategory ,androidx.core.app.NotificationCompat.Builder  setContentIntent ,androidx.core.app.NotificationCompat.Builder  setContentText ,androidx.core.app.NotificationCompat.Builder  setContentTitle ,androidx.core.app.NotificationCompat.Builder  
setOngoing ,androidx.core.app.NotificationCompat.Builder  setPriority ,androidx.core.app.NotificationCompat.Builder  setShowWhen ,androidx.core.app.NotificationCompat.Builder  	setSilent ,androidx.core.app.NotificationCompat.Builder  setSmallIcon ,androidx.core.app.NotificationCompat.Builder  
setVisibility ,androidx.core.app.NotificationCompat.Builder  MainActivity com.example.lekky  Boolean 
com.lekky.app  Build 
com.lekky.app  
CHANNEL_ID 
com.lekky.app  Class 
com.lekky.app  Context 
com.lekky.app  	Exception 
com.lekky.app  Int 
com.lekky.app  Integer 
com.lekky.app  Intent 
com.lekky.app  Log 
com.lekky.app  MainActivity 
com.lekky.app  
MethodChannel 
com.lekky.app  NOTIFICATION_ID 
com.lekky.app  Notification 
com.lekky.app  NotificationChannel 
com.lekky.app  NotificationCompat 
com.lekky.app  NotificationForegroundService 
com.lekky.app  NotificationManager 
com.lekky.app  
PendingIntent 
com.lekky.app  START_STICKY 
com.lekky.app  Service 
com.lekky.app  TAG 
com.lekky.app  android 
com.lekky.app  apply 
com.lekky.app  java 
com.lekky.app  ActivityManager com.lekky.app.MainActivity  Boolean com.lekky.app.MainActivity  CHANNEL com.lekky.app.MainActivity  Class com.lekky.app.MainActivity  Context com.lekky.app.MainActivity  	Exception com.lekky.app.MainActivity  
FlutterEngine com.lekky.app.MainActivity  Integer com.lekky.app.MainActivity  
MethodChannel com.lekky.app.MainActivity  NotificationForegroundService com.lekky.app.MainActivity  getSystemService com.lekky.app.MainActivity  isServiceRunning com.lekky.app.MainActivity  java com.lekky.app.MainActivity  Build +com.lekky.app.NotificationForegroundService  
CHANNEL_ID +com.lekky.app.NotificationForegroundService  	Companion +com.lekky.app.NotificationForegroundService  Context +com.lekky.app.NotificationForegroundService  IBinder +com.lekky.app.NotificationForegroundService  Int +com.lekky.app.NotificationForegroundService  Intent +com.lekky.app.NotificationForegroundService  Log +com.lekky.app.NotificationForegroundService  MainActivity +com.lekky.app.NotificationForegroundService  NOTIFICATION_ID +com.lekky.app.NotificationForegroundService  Notification +com.lekky.app.NotificationForegroundService  NotificationChannel +com.lekky.app.NotificationForegroundService  NotificationCompat +com.lekky.app.NotificationForegroundService  NotificationForegroundService +com.lekky.app.NotificationForegroundService  NotificationManager +com.lekky.app.NotificationForegroundService  
PendingIntent +com.lekky.app.NotificationForegroundService  START_STICKY +com.lekky.app.NotificationForegroundService  TAG +com.lekky.app.NotificationForegroundService  android +com.lekky.app.NotificationForegroundService  apply +com.lekky.app.NotificationForegroundService  createNotification +com.lekky.app.NotificationForegroundService  createNotificationChannel +com.lekky.app.NotificationForegroundService  
getANDROID +com.lekky.app.NotificationForegroundService  getAPPLY +com.lekky.app.NotificationForegroundService  
getAndroid +com.lekky.app.NotificationForegroundService  getApply +com.lekky.app.NotificationForegroundService  getSystemService +com.lekky.app.NotificationForegroundService  java +com.lekky.app.NotificationForegroundService  startForeground +com.lekky.app.NotificationForegroundService  startService +com.lekky.app.NotificationForegroundService  stopService +com.lekky.app.NotificationForegroundService  Build 5com.lekky.app.NotificationForegroundService.Companion  
CHANNEL_ID 5com.lekky.app.NotificationForegroundService.Companion  Context 5com.lekky.app.NotificationForegroundService.Companion  IBinder 5com.lekky.app.NotificationForegroundService.Companion  Int 5com.lekky.app.NotificationForegroundService.Companion  Intent 5com.lekky.app.NotificationForegroundService.Companion  Log 5com.lekky.app.NotificationForegroundService.Companion  MainActivity 5com.lekky.app.NotificationForegroundService.Companion  NOTIFICATION_ID 5com.lekky.app.NotificationForegroundService.Companion  Notification 5com.lekky.app.NotificationForegroundService.Companion  NotificationChannel 5com.lekky.app.NotificationForegroundService.Companion  NotificationCompat 5com.lekky.app.NotificationForegroundService.Companion  NotificationForegroundService 5com.lekky.app.NotificationForegroundService.Companion  NotificationManager 5com.lekky.app.NotificationForegroundService.Companion  
PendingIntent 5com.lekky.app.NotificationForegroundService.Companion  START_STICKY 5com.lekky.app.NotificationForegroundService.Companion  TAG 5com.lekky.app.NotificationForegroundService.Companion  android 5com.lekky.app.NotificationForegroundService.Companion  apply 5com.lekky.app.NotificationForegroundService.Companion  
getANDROID 5com.lekky.app.NotificationForegroundService.Companion  getAPPLY 5com.lekky.app.NotificationForegroundService.Companion  
getAndroid 5com.lekky.app.NotificationForegroundService.Companion  getApply 5com.lekky.app.NotificationForegroundService.Companion  java 5com.lekky.app.NotificationForegroundService.Companion  startService 5com.lekky.app.NotificationForegroundService.Companion  stopService 5com.lekky.app.NotificationForegroundService.Companion  Log 
io.flutter  d io.flutter.Log  FlutterActivity io.flutter.embedding.android  ActivityManager ,io.flutter.embedding.android.FlutterActivity  Boolean ,io.flutter.embedding.android.FlutterActivity  Class ,io.flutter.embedding.android.FlutterActivity  Context ,io.flutter.embedding.android.FlutterActivity  	Exception ,io.flutter.embedding.android.FlutterActivity  
FlutterEngine ,io.flutter.embedding.android.FlutterActivity  Integer ,io.flutter.embedding.android.FlutterActivity  
MethodChannel ,io.flutter.embedding.android.FlutterActivity  NotificationForegroundService ,io.flutter.embedding.android.FlutterActivity  configureFlutterEngine ,io.flutter.embedding.android.FlutterActivity  getSystemService ,io.flutter.embedding.android.FlutterActivity  isServiceRunning ,io.flutter.embedding.android.FlutterActivity  java ,io.flutter.embedding.android.FlutterActivity  
FlutterEngine io.flutter.embedding.engine  dartExecutor )io.flutter.embedding.engine.FlutterEngine  getDARTExecutor )io.flutter.embedding.engine.FlutterEngine  getDartExecutor )io.flutter.embedding.engine.FlutterEngine  setDartExecutor )io.flutter.embedding.engine.FlutterEngine  binaryMessenger -io.flutter.embedding.engine.dart.DartExecutor  getBINARYMessenger -io.flutter.embedding.engine.dart.DartExecutor  getBinaryMessenger -io.flutter.embedding.engine.dart.DartExecutor  setBinaryMessenger -io.flutter.embedding.engine.dart.DartExecutor  BinaryMessenger io.flutter.plugin.common  
MethodCall io.flutter.plugin.common  
MethodChannel io.flutter.plugin.common  method #io.flutter.plugin.common.MethodCall  Result &io.flutter.plugin.common.MethodChannel  setMethodCallHandler &io.flutter.plugin.common.MethodChannel  <SAM-CONSTRUCTOR> 8io.flutter.plugin.common.MethodChannel.MethodCallHandler  error -io.flutter.plugin.common.MethodChannel.Result  notImplemented -io.flutter.plugin.common.MethodChannel.Result  success -io.flutter.plugin.common.MethodChannel.Result  Build 	java.lang  
CHANNEL_ID 	java.lang  Class 	java.lang  Context 	java.lang  	Exception 	java.lang  Integer 	java.lang  Intent 	java.lang  Log 	java.lang  MainActivity 	java.lang  
MethodChannel 	java.lang  NOTIFICATION_ID 	java.lang  Notification 	java.lang  NotificationChannel 	java.lang  NotificationCompat 	java.lang  NotificationForegroundService 	java.lang  NotificationManager 	java.lang  
PendingIntent 	java.lang  START_STICKY 	java.lang  TAG 	java.lang  android 	java.lang  apply 	java.lang  java 	java.lang  getNAME java.lang.Class  getName java.lang.Class  name java.lang.Class  setName java.lang.Class  message java.lang.Exception  	MAX_VALUE java.lang.Integer  Any kotlin  Boolean kotlin  Build kotlin  
CHANNEL_ID kotlin  Class kotlin  Context kotlin  	Exception kotlin  	Function1 kotlin  	Function2 kotlin  Int kotlin  Integer kotlin  Intent kotlin  Log kotlin  MainActivity kotlin  
MethodChannel kotlin  NOTIFICATION_ID kotlin  Nothing kotlin  Notification kotlin  NotificationChannel kotlin  NotificationCompat kotlin  NotificationForegroundService kotlin  NotificationManager kotlin  
PendingIntent kotlin  START_STICKY kotlin  String kotlin  TAG kotlin  android kotlin  apply kotlin  java kotlin  Build kotlin.annotation  
CHANNEL_ID kotlin.annotation  Class kotlin.annotation  Context kotlin.annotation  	Exception kotlin.annotation  Integer kotlin.annotation  Intent kotlin.annotation  Log kotlin.annotation  MainActivity kotlin.annotation  
MethodChannel kotlin.annotation  NOTIFICATION_ID kotlin.annotation  Notification kotlin.annotation  NotificationChannel kotlin.annotation  NotificationCompat kotlin.annotation  NotificationForegroundService kotlin.annotation  NotificationManager kotlin.annotation  
PendingIntent kotlin.annotation  START_STICKY kotlin.annotation  TAG kotlin.annotation  android kotlin.annotation  apply kotlin.annotation  java kotlin.annotation  Build kotlin.collections  
CHANNEL_ID kotlin.collections  Class kotlin.collections  Context kotlin.collections  	Exception kotlin.collections  Integer kotlin.collections  Intent kotlin.collections  Log kotlin.collections  MainActivity kotlin.collections  
MethodChannel kotlin.collections  MutableList kotlin.collections  NOTIFICATION_ID kotlin.collections  Notification kotlin.collections  NotificationChannel kotlin.collections  NotificationCompat kotlin.collections  NotificationForegroundService kotlin.collections  NotificationManager kotlin.collections  
PendingIntent kotlin.collections  START_STICKY kotlin.collections  TAG kotlin.collections  android kotlin.collections  apply kotlin.collections  java kotlin.collections  Build kotlin.comparisons  
CHANNEL_ID kotlin.comparisons  Class kotlin.comparisons  Context kotlin.comparisons  	Exception kotlin.comparisons  Integer kotlin.comparisons  Intent kotlin.comparisons  Log kotlin.comparisons  MainActivity kotlin.comparisons  
MethodChannel kotlin.comparisons  NOTIFICATION_ID kotlin.comparisons  Notification kotlin.comparisons  NotificationChannel kotlin.comparisons  NotificationCompat kotlin.comparisons  NotificationForegroundService kotlin.comparisons  NotificationManager kotlin.comparisons  
PendingIntent kotlin.comparisons  START_STICKY kotlin.comparisons  TAG kotlin.comparisons  android kotlin.comparisons  apply kotlin.comparisons  java kotlin.comparisons  Build 	kotlin.io  
CHANNEL_ID 	kotlin.io  Class 	kotlin.io  Context 	kotlin.io  	Exception 	kotlin.io  Integer 	kotlin.io  Intent 	kotlin.io  Log 	kotlin.io  MainActivity 	kotlin.io  
MethodChannel 	kotlin.io  NOTIFICATION_ID 	kotlin.io  Notification 	kotlin.io  NotificationChannel 	kotlin.io  NotificationCompat 	kotlin.io  NotificationForegroundService 	kotlin.io  NotificationManager 	kotlin.io  
PendingIntent 	kotlin.io  START_STICKY 	kotlin.io  TAG 	kotlin.io  android 	kotlin.io  apply 	kotlin.io  java 	kotlin.io  Build 
kotlin.jvm  
CHANNEL_ID 
kotlin.jvm  Class 
kotlin.jvm  Context 
kotlin.jvm  	Exception 
kotlin.jvm  Integer 
kotlin.jvm  Intent 
kotlin.jvm  Log 
kotlin.jvm  MainActivity 
kotlin.jvm  
MethodChannel 
kotlin.jvm  NOTIFICATION_ID 
kotlin.jvm  Notification 
kotlin.jvm  NotificationChannel 
kotlin.jvm  NotificationCompat 
kotlin.jvm  NotificationForegroundService 
kotlin.jvm  NotificationManager 
kotlin.jvm  
PendingIntent 
kotlin.jvm  START_STICKY 
kotlin.jvm  TAG 
kotlin.jvm  android 
kotlin.jvm  apply 
kotlin.jvm  java 
kotlin.jvm  Build 
kotlin.ranges  
CHANNEL_ID 
kotlin.ranges  Class 
kotlin.ranges  Context 
kotlin.ranges  	Exception 
kotlin.ranges  Integer 
kotlin.ranges  Intent 
kotlin.ranges  Log 
kotlin.ranges  MainActivity 
kotlin.ranges  
MethodChannel 
kotlin.ranges  NOTIFICATION_ID 
kotlin.ranges  Notification 
kotlin.ranges  NotificationChannel 
kotlin.ranges  NotificationCompat 
kotlin.ranges  NotificationForegroundService 
kotlin.ranges  NotificationManager 
kotlin.ranges  
PendingIntent 
kotlin.ranges  START_STICKY 
kotlin.ranges  TAG 
kotlin.ranges  android 
kotlin.ranges  apply 
kotlin.ranges  java 
kotlin.ranges  KClass kotlin.reflect  getJAVA kotlin.reflect.KClass  getJava kotlin.reflect.KClass  java kotlin.reflect.KClass  Build kotlin.sequences  
CHANNEL_ID kotlin.sequences  Class kotlin.sequences  Context kotlin.sequences  	Exception kotlin.sequences  Integer kotlin.sequences  Intent kotlin.sequences  Log kotlin.sequences  MainActivity kotlin.sequences  
MethodChannel kotlin.sequences  NOTIFICATION_ID kotlin.sequences  Notification kotlin.sequences  NotificationChannel kotlin.sequences  NotificationCompat kotlin.sequences  NotificationForegroundService kotlin.sequences  NotificationManager kotlin.sequences  
PendingIntent kotlin.sequences  START_STICKY kotlin.sequences  TAG kotlin.sequences  android kotlin.sequences  apply kotlin.sequences  java kotlin.sequences  Build kotlin.text  
CHANNEL_ID kotlin.text  Class kotlin.text  Context kotlin.text  	Exception kotlin.text  Integer kotlin.text  Intent kotlin.text  Log kotlin.text  MainActivity kotlin.text  
MethodChannel kotlin.text  NOTIFICATION_ID kotlin.text  Notification kotlin.text  NotificationChannel kotlin.text  NotificationCompat kotlin.text  NotificationForegroundService kotlin.text  NotificationManager kotlin.text  
PendingIntent kotlin.text  START_STICKY kotlin.text  TAG kotlin.text  android kotlin.text  apply kotlin.text  java kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               