import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/widgets/app_banner.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/providers/settings_provider.dart';
import '../../../../core/providers/settings_navigation_provider.dart';
import '../../../../core/models/settings_state.dart';
import '../../domain/models/settings_category.dart';
import '../../domain/models/settings_sub_branch.dart';
import '../widgets/expandable_settings_category.dart';

/// Settings screen for the app
class SettingsScreen extends ConsumerStatefulWidget {
  /// Constructor
  const SettingsScreen({super.key});

  @override
  ConsumerState<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends ConsumerState<SettingsScreen> {
  final ScrollController _scrollController = ScrollController();
  final Map<int, GlobalKey> _categoryKeys = {};

  @override
  void initState() {
    super.initState();
    // Initialize category keys for height measurement
    for (int i = 0; i <= 6; i++) {
      _categoryKeys[i] = GlobalKey();
    }

    // Check for route parameters to set expanded category
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _handleRouteParameters();
    });

    // Listen to expansion state changes for automatic scrolling
    ref.listenManual(settingsNavigationProvider, (previous, next) {
      if (previous?.expandedCategoryIndex != next.expandedCategoryIndex) {
        _handleCategoryExpansionChange(next.expandedCategoryIndex);
      }
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  /// Handle route parameters to set expanded category
  /// Updated for new category order: 0=Alerts, 1=Region, 2=Date, 3=Data, 4=Appearance, 5=About, 6=Donate
  void _handleRouteParameters() {
    final currentUri = GoRouter.of(context).routeInformationProvider.value.uri;
    final expandedParam = currentUri.queryParameters['expanded'];

    if (expandedParam != null) {
      final categoryIndex = int.tryParse(expandedParam);
      if (categoryIndex != null && categoryIndex >= 0 && categoryIndex <= 6) {
        ref
            .read(settingsNavigationProvider.notifier)
            .setExpandedCategory(categoryIndex);
        _scrollToCategory(categoryIndex);
      }
    } else {
      // Clear expanded categories when no expanded parameter is present
      // This ensures clean state when navigating from other main tabs
      ref.read(settingsNavigationProvider.notifier).clearExpandedCategory();
    }
  }

  @override
  Widget build(BuildContext context) {
    final settingsAsync = ref.watch(settingsProvider);

    return PopScope(
      canPop: false, // Block system back — we control everything
      onPopInvoked: (didPop) async {
        if (didPop) return;

        // Navigate to Dashboard
        if (context.mounted) {
          context.go(AppConstants.routeHome);
        }
      },
      child: Scaffold(
        body: settingsAsync.when(
          loading: () => const Center(
            child: CircularProgressIndicator(),
          ),
          error: (error, stackTrace) => Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  color: Colors.red,
                  size: 48,
                ),
                const SizedBox(height: 16),
                const Text(
                  'An error occurred while loading settings',
                  style: TextStyle(color: Colors.red),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () {
                    ref.invalidate(settingsProvider);
                  },
                  child: const Text('Retry'),
                ),
              ],
            ),
          ),
          data: (settings) {
            return Column(
              children: [
                // App Banner with "Settings" title using gradient colors
                AppBanner(
                  message: 'Settings',
                  gradientColors: AppColors.getSettingsMainCardGradient(
                      Theme.of(context).brightness == Brightness.dark),
                  textColor: AppColors.getAppBarTextColor('settings',
                      Theme.of(context).brightness == Brightness.dark),
                ),
                Expanded(
                  child: ListView(
                    controller: _scrollController,
                    padding: const EdgeInsets.all(16.0),
                    children: [
                      // Alerts & Notifications (Priority 1 - Core App Value)
                      Container(
                        key: _categoryKeys[0],
                        child:
                            _buildNotificationsCategory(context, settings, 0),
                      ),

                      const SizedBox(height: 8),

                      // Region (Priority 2 - Essential)
                      Container(
                        key: _categoryKeys[1],
                        child: _buildRegionCategory(context, settings, 1),
                      ),

                      const SizedBox(height: 8),

                      // Date Settings (Priority 3 - Important)
                      Container(
                        key: _categoryKeys[2],
                        child: _buildDateSettingsCategory(context, settings, 2),
                      ),

                      const SizedBox(height: 8),

                      // Data Management (Priority 4 - Critical Data Safety)
                      Container(
                        key: _categoryKeys[3],
                        child: _buildDataBackupCategory(context, settings, 3),
                      ),

                      const SizedBox(height: 8),

                      // Appearance (Priority 5 - Personal Preference)
                      Container(
                        key: _categoryKeys[4],
                        child: _buildAppearanceCategory(context, settings, 4),
                      ),

                      const SizedBox(height: 8),

                      // About (Priority 6 - Support & Information)
                      Container(
                        key: _categoryKeys[5],
                        child: _buildAboutCategory(context, settings, 5),
                      ),

                      const SizedBox(height: 8),

                      // Donate (Priority 7 - Optional Support)
                      Container(
                        key: _categoryKeys[6],
                        child: _buildDonateCategory(context, settings, 6),
                      ),

                      const SizedBox(height: 8),
                    ],
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  /// Handle category expansion state changes with smart scrolling
  void _handleCategoryExpansionChange(int? expandedCategoryIndex) {
    if (expandedCategoryIndex != null && expandedCategoryIndex >= 0) {
      // Delay scroll to allow expansion animation to complete
      // ExpandableSettingsCategory uses 200ms animation, so wait 250ms for completion
      Future.delayed(const Duration(milliseconds: 250), () {
        _scrollToExpandedCategory(expandedCategoryIndex);
      });
    } else {
      // Category collapsed - adjust to optimal viewing position
      _handleCategoryCollapse();
    }
  }

  /// Smart scroll to show entire expanded category content
  void _scrollToExpandedCategory(int categoryIndex) {
    if (!_scrollController.hasClients) return;

    final categoryKey = _categoryKeys[categoryIndex];
    if (categoryKey?.currentContext == null) return;

    final RenderBox? categoryBox =
        categoryKey!.currentContext!.findRenderObject() as RenderBox?;
    if (categoryBox == null) return;

    // Get category position and size
    final categoryPosition = categoryBox.localToGlobal(Offset.zero);
    final categoryHeight = categoryBox.size.height;

    // Get screen dimensions
    final screenHeight = MediaQuery.of(context).size.height;
    const appBarHeight = 100.0; // Approximate banner height
    const navigationBarHeight =
        kBottomNavigationBarHeight; // Standard Flutter navigation bar height (56px)
    final availableHeight = screenHeight - appBarHeight - navigationBarHeight;

    // Calculate optimal scroll position
    final currentScrollOffset = _scrollController.offset;
    final categoryTopInScrollView =
        categoryPosition.dy - appBarHeight + currentScrollOffset;

    // Smart positioning logic
    double targetScrollOffset;

    // Special handling for About category (index 5) to include gap to Donate
    if (categoryIndex == 5) {
      // About category - dynamic screen solution for optimal positioning
      // Calculate space needed to show About card + gap + partial Donate card

      // Aggressive scroll up to ensure Donate card visibility
      // Calculate much more space needed to show Donate card properly
      const estimatedDonateCardHeight = 90.0;
      const gapBetweenCards = 8.0;
      const partialDonateVisibility = 0.8; // Show 80% of Donate card

      // Calculate total space needed below About card
      final spaceNeededBelow = gapBetweenCards +
          (estimatedDonateCardHeight * partialDonateVisibility);

      // Much more aggressive scroll up - use larger percentage of available height
      final aggressiveScrollSpace =
          availableHeight * 0.35; // 35% of screen height
      final minScrollSpace = spaceNeededBelow + 50.0; // Minimum 50px extra
      final maxScrollSpace = availableHeight * 0.5; // Maximum 50% of screen

      final finalScrollSpace =
          aggressiveScrollSpace.clamp(minScrollSpace, maxScrollSpace);

      // Scroll up aggressively to show both About and Donate
      targetScrollOffset = categoryTopInScrollView - finalScrollSpace;
    } else if (categoryHeight <= availableHeight * 0.8) {
      // Category fits comfortably - center it with some top padding
      targetScrollOffset =
          categoryTopInScrollView - (availableHeight - categoryHeight) * 0.2;
    } else {
      // Large category - position at top with minimal padding
      targetScrollOffset = categoryTopInScrollView - 20;
    }

    // Ensure we don't scroll beyond bounds
    final maxScrollExtent = _scrollController.position.maxScrollExtent;
    targetScrollOffset = targetScrollOffset.clamp(0.0, maxScrollExtent);

    // Animate to target position
    _scrollController.animateTo(
      targetScrollOffset,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  /// Handle category collapse with optimal positioning
  void _handleCategoryCollapse() {
    // Stay at current position for better UX
    // User can manually scroll if needed
  }

  /// Legacy method for route parameter handling
  void _scrollToCategory(int categoryIndex) {
    // Use the enhanced scroll method
    _scrollToExpandedCategory(categoryIndex);
  }

  // Helper methods to build each category

  Widget _buildRegionCategory(
      BuildContext context, SettingsState settings, int index) {
    final navigationState = ref.watch(settingsNavigationProvider);
    final expandedCategoryIndex = navigationState.expandedCategoryIndex;

    final category = SettingsCategory(
      title: 'Region',
      icon: Icons.public,
      iconColor: Colors.blue, // Essential priority - blue for global impact
      currentValue: "Language & Currency",
      subBranches: [
        SettingsSubBranch(
          title: 'Language',
          icon: Icons.language,
          iconColor: Colors.blue,
          routePath: '/main-settings/language',
          currentValue: settings.language,
        ),
        SettingsSubBranch(
          title: 'Currency',
          icon: Icons.currency_exchange,
          iconColor: Colors.blue,
          routePath: '/main-settings/currency',
          currentValue: settings.currency,
        ),
      ],
    );

    return ExpandableSettingsCategory(
      category: category,
      isExpanded: expandedCategoryIndex == index,
      onExpansionChanged: (expanded) {
        ref.read(settingsNavigationProvider.notifier).toggleCategory(index);
      },
    );
  }

  Widget _buildNotificationsCategory(
      BuildContext context, SettingsState settings, int index) {
    final navigationState = ref.watch(settingsNavigationProvider);
    final expandedCategoryIndex = navigationState.expandedCategoryIndex;

    final category = SettingsCategory(
      title: 'Alerts & Notifications',
      icon: Icons.notifications,
      iconColor: Colors.red, // Critical priority - red for core app value
      currentValue: 'Core App Functionality',
      subBranches: [
        SettingsSubBranch(
          title: 'Alert Threshold',
          icon: Icons.warning,
          iconColor: Colors.orange,
          routePath: '/main-settings/alert-threshold',
          currentValue: settings.formattedAlertThreshold,
        ),
        SettingsSubBranch(
          title: 'Days in Advance',
          icon: Icons.calendar_today,
          iconColor: Colors.orange,
          routePath: '/main-settings/days-advance',
          currentValue: settings.formattedDaysInAdvance,
        ),
        const SettingsSubBranch(
          title: 'Notification Types',
          icon: Icons.notifications_active,
          iconColor: Colors.red,
          routePath: '/main-settings/notification-types',
        ),
        const SettingsSubBranch(
          title: 'Reminders',
          icon: Icons.alarm,
          iconColor: Colors.red,
          routePath: '/main-settings/reminders',
        ),
        const SettingsSubBranch(
          title: 'Alarm Settings',
          icon: Icons.volume_up,
          iconColor: Colors.orange,
          routePath: '/main-settings/alarm-settings',
        ),
        const SettingsSubBranch(
          title: 'Notification Utilities',
          icon: Icons.troubleshoot,
          iconColor: Colors.purple,
          routePath: '/main-settings/notification-utilities',
        ),
      ],
    );

    return ExpandableSettingsCategory(
      category: category,
      isExpanded: expandedCategoryIndex == index,
      onExpansionChanged: (expanded) {
        ref.read(settingsNavigationProvider.notifier).toggleCategory(index);
      },
    );
  }

  Widget _buildDateSettingsCategory(
      BuildContext context, SettingsState settings, int index) {
    final navigationState = ref.watch(settingsNavigationProvider);
    final expandedCategoryIndex = navigationState.expandedCategoryIndex;

    final category = SettingsCategory(
      title: 'Date Settings',
      icon: Icons.calendar_today,
      iconColor: Colors.blue, // Important priority - blue for data presentation
      currentValue: 'Data Presentation',
      subBranches: [
        SettingsSubBranch(
          title: 'Date Format',
          icon: Icons.date_range,
          iconColor: Colors.blue,
          routePath: '/main-settings/date-format',
          currentValue: settings.dateFormat,
        ),
        SettingsSubBranch(
          title: 'Time Display',
          icon: Icons.access_time,
          iconColor: Colors.blue,
          routePath: '/main-settings/time-display',
          currentValue: settings.showTimeWithDate ? 'Shown' : 'Hidden',
        ),
      ],
    );

    return ExpandableSettingsCategory(
      category: category,
      isExpanded: expandedCategoryIndex == index,
      onExpansionChanged: (expanded) {
        ref.read(settingsNavigationProvider.notifier).toggleCategory(index);
      },
    );
  }

  Widget _buildAppearanceCategory(
      BuildContext context, SettingsState settings, int index) {
    final navigationState = ref.watch(settingsNavigationProvider);
    final expandedCategoryIndex = navigationState.expandedCategoryIndex;

    final category = SettingsCategory(
      title: 'Appearance',
      icon: Icons.palette,
      iconColor:
          Colors.green, // Optional priority - green for personal preference
      currentValue: 'Personal Preference',
      subBranches: [
        SettingsSubBranch(
          title: 'Theme Options',
          icon: Icons.brightness_6,
          iconColor: Colors.green,
          routePath: '/main-settings/theme',
          currentValue: settings.themeModeDisplayName,
        ),
      ],
    );

    return ExpandableSettingsCategory(
      category: category,
      isExpanded: expandedCategoryIndex == index,
      onExpansionChanged: (expanded) {
        ref.read(settingsNavigationProvider.notifier).toggleCategory(index);
      },
    );
  }

  Widget _buildDataBackupCategory(
      BuildContext context, SettingsState settings, int index) {
    final navigationState = ref.watch(settingsNavigationProvider);
    final expandedCategoryIndex = navigationState.expandedCategoryIndex;

    final category = SettingsCategory(
      title: 'Data Management',
      icon: Icons.backup,
      iconColor: Colors.orange, // Important priority - orange for data safety
      currentValue: 'Data Safety & Backup',
      subBranches: [
        const SettingsSubBranch(
          title: 'Export Data',
          icon: Icons.file_download,
          iconColor: Colors.orange,
          routePath: '/main-settings/csv/export',
        ),
        const SettingsSubBranch(
          title: 'Import Data',
          icon: Icons.file_upload,
          iconColor: Colors.orange,
          routePath: '/main-settings/csv/import',
        ),
        const SettingsSubBranch(
          title: 'Delete All Data',
          icon: Icons.delete_forever,
          iconColor: Colors.red,
          routePath: '/main-settings/data/delete-all',
        ),
      ],
    );

    return ExpandableSettingsCategory(
      category: category,
      isExpanded: expandedCategoryIndex == index,
      onExpansionChanged: (expanded) {
        ref.read(settingsNavigationProvider.notifier).toggleCategory(index);
      },
    );
  }

  Widget _buildAboutCategory(
      BuildContext context, SettingsState settings, int index) {
    final navigationState = ref.watch(settingsNavigationProvider);
    final expandedCategoryIndex = navigationState.expandedCategoryIndex;

    final category = SettingsCategory(
      title: 'About',
      icon: Icons.info,
      iconColor: Colors.grey, // Support priority - grey for information
      currentValue: 'Support & Information',
      subBranches: [
        const SettingsSubBranch(
          title: 'App Information',
          icon: Icons.info_outline,
          iconColor: Colors.grey,
          routePath: '/main-settings/app-information',
        ),
        const SettingsSubBranch(
          title: 'Update',
          icon: Icons.update,
          iconColor: Colors.grey,
          routePath: '/main-settings/update',
        ),
        const SettingsSubBranch(
          title: 'Tips & Tricks',
          icon: Icons.lightbulb,
          iconColor: Colors.amber,
          routePath: '/main-settings/tips-tricks',
        ),
      ],
    );

    return ExpandableSettingsCategory(
      category: category,
      isExpanded: expandedCategoryIndex == index,
      onExpansionChanged: (expanded) {
        ref.read(settingsNavigationProvider.notifier).toggleCategory(index);
      },
    );
  }

  Widget _buildDonateCategory(
      BuildContext context, SettingsState settings, int index) {
    final category = SettingsCategory(
      title: 'Donate',
      icon: Icons.favorite,
      iconColor: Colors.red,
      currentValue: 'Support Lekky',
      subBranches: [], // Empty since we navigate directly
    );

    return Card(
      margin: const EdgeInsets.only(bottom: 8.0),
      color: Theme.of(context).brightness == Brightness.dark
          ? const Color(0xFF1E1E1E)
          : Colors.white,
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: InkWell(
        onTap: () {
          // Scroll to show the Donate category optimally before navigation
          _scrollToExpandedCategory(index);
          // Clear all expanded categories immediately (like when navigating from other main tabs)
          ref.read(settingsNavigationProvider.notifier).clearExpandedCategory();
          // Small delay to allow scroll animation to start, then navigate
          Future.delayed(const Duration(milliseconds: 100), () {
            context.go('/main-settings/donate');
          });
        },
        borderRadius: BorderRadius.circular(12.0),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              Icon(
                category.icon,
                color: category.iconColor,
                size: 24,
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      category.title,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Colors.white
                            : Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      category.currentValue!,
                      style: TextStyle(
                        fontSize: 14,
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Colors.white70
                            : Colors.black54,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.chevron_right,
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.white
                    : Colors.black87,
                size: 24,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
