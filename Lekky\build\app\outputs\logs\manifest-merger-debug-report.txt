-- Merging decision tree log ---
application
INJECTED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:42:5-104:19
INJECTED from D:\000.Workspace\Lekky\android\app\src\debug\AndroidManifest.xml
MERGED from [:url_launcher_android] D:\000.Workspace\Lekky\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-12:19
MERGED from [:url_launcher_android] D:\000.Workspace\Lekky\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-12:19
MERGED from [:alarm] D:\000.Workspace\Lekky\build\alarm\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:5-27:19
MERGED from [:alarm] D:\000.Workspace\Lekky\build\alarm\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:5-27:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\76e7c0e46e1e41ca65da1405b9eb1cd5\transformed\jetified-window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\76e7c0e46e1e41ca65da1405b9eb1cd5\transformed\jetified-window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\33e7cd33ba25126286e7a29ce6645aa9\transformed\material-1.9.0\AndroidManifest.xml:24:5-20
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\33e7cd33ba25126286e7a29ce6645aa9\transformed\material-1.9.0\AndroidManifest.xml:24:5-20
MERGED from [com.google.android.play:app-update:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\767dc9d8c1dfe9a3759a834576eb57d7\transformed\jetified-app-update-2.1.0\AndroidManifest.xml:11:5-12:19
MERGED from [com.google.android.play:app-update:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\767dc9d8c1dfe9a3759a834576eb57d7\transformed\jetified-app-update-2.1.0\AndroidManifest.xml:11:5-12:19
MERGED from [com.google.android.play:feature-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\48e5595d725ffd7a1c5cbadee2d24a51\transformed\jetified-feature-delivery-2.1.0\AndroidManifest.xml:11:5-12:19
MERGED from [com.google.android.play:feature-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\48e5595d725ffd7a1c5cbadee2d24a51\transformed\jetified-feature-delivery-2.1.0\AndroidManifest.xml:11:5-12:19
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\b69303ac21562a827ede65f286c3b864\transformed\jetified-play-services-tasks-18.0.2\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\b69303ac21562a827ede65f286c3b864\transformed\jetified-play-services-tasks-18.0.2\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\fa41a8f4d43ebdb1a2bdc7a1d2c6fe82\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\fa41a8f4d43ebdb1a2bdc7a1d2c6fe82\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79b3b600fb3b916d065f9ce1405580fd\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79b3b600fb3b916d065f9ce1405580fd\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\ffce1bb1ba7d6308e78ae0ff3994d469\transformed\room-runtime-2.5.0\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\ffce1bb1ba7d6308e78ae0ff3994d469\transformed\room-runtime-2.5.0\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\63063b26a8d5be2fb0fed04707a2e278\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\63063b26a8d5be2fb0fed04707a2e278\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-4\6e1dc6c49b9b4e7630c6b57963983704\transformed\core-1.10.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-4\6e1dc6c49b9b4e7630c6b57963983704\transformed\core-1.10.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\2a5f274ded50dacc86153bab5a91cc40\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\2a5f274ded50dacc86153bab5a91cc40\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\1758dd11e3be0aa50b0e6559e83f82a9\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\1758dd11e3be0aa50b0e6559e83f82a9\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\c48568960db0ea95ff408bd73d1cd09a\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\c48568960db0ea95ff408bd73d1cd09a\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\ce88c44a4a968999ee4b64dbbb01246d\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:11:5-19:19
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\ce88c44a4a968999ee4b64dbbb01246d\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:11:5-19:19
	android:extractNativeLibs
		INJECTED from D:\000.Workspace\Lekky\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-4\6e1dc6c49b9b4e7630c6b57963983704\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
	android:name
		INJECTED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml
manifest
ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:1:1-129:12
MERGED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:1:1-129:12
INJECTED from D:\000.Workspace\Lekky\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from D:\000.Workspace\Lekky\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from D:\000.Workspace\Lekky\android\app\src\debug\AndroidManifest.xml:1:1-7:12
MERGED from [:file_picker] D:\000.Workspace\Lekky\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-17:12
MERGED from [:flutter_plugin_android_lifecycle] D:\000.Workspace\Lekky\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:sqflite] D:\000.Workspace\Lekky\build\sqflite\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:flutter_local_notifications] D:\000.Workspace\Lekky\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-10:12
MERGED from [:url_launcher_android] D:\000.Workspace\Lekky\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-14:12
MERGED from [:path_provider_android] D:\000.Workspace\Lekky\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:flutter_secure_storage] D:\000.Workspace\Lekky\build\flutter_secure_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-10:12
MERGED from [:alarm] D:\000.Workspace\Lekky\build\alarm\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-29:12
MERGED from [:flutter_fgbg] D:\000.Workspace\Lekky\build\flutter_fgbg\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:flutter_native_splash] D:\000.Workspace\Lekky\build\flutter_native_splash\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:shared_preferences_android] D:\000.Workspace\Lekky\build\shared_preferences_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:permission_handler_android] D:\000.Workspace\Lekky\build\permission_handler_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:integration_test] D:\000.Workspace\Lekky\build\integration_test\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:1-12:12
MERGED from [:file_saver] D:\000.Workspace\Lekky\build\file_saver\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:package_info_plus] D:\000.Workspace\Lekky\build\package_info_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:device_info_plus] D:\000.Workspace\Lekky\build\device_info_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:workmanager] D:\000.Workspace\Lekky\build\workmanager\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.window:window-java:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cdacfaa49b43f0b8e5751d92c0b5e4a8\transformed\jetified-window-java-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\76e7c0e46e1e41ca65da1405b9eb1cd5\transformed\jetified-window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\33e7cd33ba25126286e7a29ce6645aa9\transformed\material-1.9.0\AndroidManifest.xml:17:1-26:12
MERGED from [com.google.android.play:app-update:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\767dc9d8c1dfe9a3759a834576eb57d7\transformed\jetified-app-update-2.1.0\AndroidManifest.xml:2:1-14:12
MERGED from [com.google.android.play:feature-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\48e5595d725ffd7a1c5cbadee2d24a51\transformed\jetified-feature-delivery-2.1.0\AndroidManifest.xml:2:1-14:12
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\b69303ac21562a827ede65f286c3b864\transformed\jetified-play-services-tasks-18.0.2\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\fa41a8f4d43ebdb1a2bdc7a1d2c6fe82\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79b3b600fb3b916d065f9ce1405580fd\transformed\constraintlayout-2.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\e4725ad2ec266c1f3d48d9d3d3128614\transformed\jetified-appcompat-resources-1.5.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\59b512fe865bec571721add8e6d44c7d\transformed\appcompat-1.5.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0bdc6b6c00c70c057f86cfee33dfeb3f\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-4\30815b1d4166c0c0244953ad3928dd40\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\7a5cf8f2ab36e18f9cb80ea2e0cdc1a7\transformed\jetified-activity-1.5.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7cc8374f835c9c1166e236439af6dc56\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\65f154d63c15b7fc44b1e2c67366acc9\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b11131fe164bea9d1c2ba190d8dcd286\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\b5708d878f9c65cbfc390dbd179742d7\transformed\lifecycle-viewmodel-2.5.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\66e24abd468c391ce79e1be0c39e41e1\transformed\jetified-lifecycle-viewmodel-savedstate-2.5.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\ffce1bb1ba7d6308e78ae0ff3994d469\transformed\room-runtime-2.5.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.media:media:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\cb77ff9286007a8143de4cd38a9909a4\transformed\media-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.browser:browser:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\b6a20bfcf076cfb90273b0a64bffb348\transformed\browser-1.5.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\07354d9cdbbea0612e8a514a4fce0df0\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\ede0346f692b4c87bee695987057451d\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\e64bb8ba5ceca538c85fb9a0930d16fd\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\7c3148cbc4d7fe4febd157ae6b3ae3db\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\67fef3903dc7dfdb2273c51ce67a409c\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\2be508f60f4457696ca8937cb2b9db31\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\63063b26a8d5be2fb0fed04707a2e278\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\a88c8b481acb52f3a4bfeb2374d5377d\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fd461721109da9d2cd57f84f9f45fd20\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\dffc091555c1f9431e93e40c2323fcda\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-4\28e62e3ce1c18f22838066d155aeee90\transformed\jetified-core-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-4\6e1dc6c49b9b4e7630c6b57963983704\transformed\core-1.10.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\transforms-4\ade3d854e511a64ccff7f22bf0e12aba\transformed\jetified-security-crypto-1.1.0-alpha06\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.test:rules:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\8079e3865619c68467fdf1a6e7e7c912\transformed\rules-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.test.espresso:espresso-core:3.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\377c7c77789a01bb5e5fe9bf8f7b34ef\transformed\espresso-core-3.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.test:runner:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\d74b85b36d30dc1a8e0e0046cb52ed04\transformed\runner-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\2a5f274ded50dacc86153bab5a91cc40\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a076ace06b5c080e326a1aff8e74515\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\1758dd11e3be0aa50b0e6559e83f82a9\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:17:1-37:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\c48568960db0ea95ff408bd73d1cd09a\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\1375740d2c2cf2a30d23306e1504e17c\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6431675dbcebd02667ecbfc027fe6f1e\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.test:monitor:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\ec6270dd602c2300d34c6facdfd3a1d8\transformed\monitor-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\66fb3d43cb3a86f46934dd953cf635f7\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0a6801872af18637c49e16ca97c54755\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\c2d243d8236fa183efe93838b9ca306a\transformed\lifecycle-livedata-2.1.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\dc99ecf8f602463761a4be706db32bc0\transformed\jetified-lifecycle-service-2.1.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\4e58d9cfab31179d3452d400b573a90e\transformed\lifecycle-runtime-2.5.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\31e85674cdac0fc0b61ff2b7afb694e2\transformed\lifecycle-livedata-core-2.5.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\7e2b8d11ccff5fcd582a4ed44f03aade\transformed\core-runtime-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\779bc8cf66cf76ffbd6b861b4274d665\transformed\jetified-savedstate-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ee5c1ccb835017032ade377a5d2be3eb\transformed\sqlite-2.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0398ad951430f62fa74fbbd37bace3b0\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\287570b0df0c27a7af4fe4bca0025a11\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\31b0028aec0dcade7346db1bf86855eb\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\09304b40a2bfb0f9741f03fa0a5319f2\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\ce88c44a4a968999ee4b64dbbb01246d\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:2:1-21:12
MERGED from [androidx.test.espresso:espresso-idling-resource:3.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\6f73da4199dfd93b79ec2a11e3749369\transformed\espresso-idling-resource-3.2.0\AndroidManifest.xml:17:1-24:12
	package
		INJECTED from D:\000.Workspace\Lekky\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from D:\000.Workspace\Lekky\android\app\src\debug\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:2:5-51
		ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:2:5-51
	android:versionCode
		INJECTED from D:\000.Workspace\Lekky\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:4:5-76
MERGED from [:flutter_local_notifications] D:\000.Workspace\Lekky\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-77
MERGED from [:flutter_local_notifications] D:\000.Workspace\Lekky\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-77
MERGED from [:alarm] D:\000.Workspace\Lekky\build\alarm\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:5-77
MERGED from [:alarm] D:\000.Workspace\Lekky\build\alarm\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:5-77
	android:name
		ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:4:22-74
uses-permission#android.permission.SCHEDULE_EXACT_ALARM
ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:6:5-78
MERGED from [:alarm] D:\000.Workspace\Lekky\build\alarm\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:5-79
MERGED from [:alarm] D:\000.Workspace\Lekky\build\alarm\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:5-79
	android:name
		ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:6:22-76
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:8:5-80
MERGED from [:alarm] D:\000.Workspace\Lekky\build\alarm\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-81
MERGED from [:alarm] D:\000.Workspace\Lekky\build\alarm\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-81
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:5-81
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:25:5-81
	android:name
		ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:8:22-78
uses-permission#android.permission.WAKE_LOCK
ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:10:5-67
MERGED from [:alarm] D:\000.Workspace\Lekky\build\alarm\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
MERGED from [:alarm] D:\000.Workspace\Lekky\build\alarm\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:23:5-68
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:23:5-68
	android:name
		ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:10:22-65
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:11:5-76
MERGED from [:alarm] D:\000.Workspace\Lekky\build\alarm\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-77
MERGED from [:alarm] D:\000.Workspace\Lekky\build\alarm\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-77
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:26:5-77
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:26:5-77
	android:name
		ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:11:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_DATA_SYNC
ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:12:5-86
	android:name
		ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:12:22-84
uses-permission#android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS
ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:14:5-94
	android:name
		ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:14:22-92
uses-permission#android.permission.USE_EXACT_ALARM
ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:16:5-73
MERGED from [:alarm] D:\000.Workspace\Lekky\build\alarm\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:5-74
MERGED from [:alarm] D:\000.Workspace\Lekky\build\alarm\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:5-74
	android:name
		ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:16:22-71
uses-permission#android.permission.VIBRATE
ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:18:5-65
MERGED from [:flutter_local_notifications] D:\000.Workspace\Lekky\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
MERGED from [:flutter_local_notifications] D:\000.Workspace\Lekky\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
MERGED from [:alarm] D:\000.Workspace\Lekky\build\alarm\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-66
MERGED from [:alarm] D:\000.Workspace\Lekky\build\alarm\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-66
	android:name
		ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:18:22-63
uses-permission#android.permission.USE_FULL_SCREEN_INTENT
ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:19:5-80
MERGED from [:alarm] D:\000.Workspace\Lekky\build\alarm\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-81
MERGED from [:alarm] D:\000.Workspace\Lekky\build\alarm\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-81
	android:name
		ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:19:22-78
uses-permission#android.permission.ACCESS_NOTIFICATION_POLICY
ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:20:5-84
MERGED from [:alarm] D:\000.Workspace\Lekky\build\alarm\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:5-85
MERGED from [:alarm] D:\000.Workspace\Lekky\build\alarm\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:5-85
	android:name
		ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:20:22-82
uses-permission#android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK
ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:21:5-91
MERGED from [:alarm] D:\000.Workspace\Lekky\build\alarm\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:5-92
MERGED from [:alarm] D:\000.Workspace\Lekky\build\alarm\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:5-92
	android:name
		ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:21:22-89
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:24:5-26:38
MERGED from [:file_picker] D:\000.Workspace\Lekky\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-80
MERGED from [:file_picker] D:\000.Workspace\Lekky\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-80
MERGED from [:alarm] D:\000.Workspace\Lekky\build\alarm\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:5-80
MERGED from [:alarm] D:\000.Workspace\Lekky\build\alarm\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:5-80
	android:maxSdkVersion
		ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:26:9-35
	android:name
		ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:25:9-64
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:27:5-29:38
	android:maxSdkVersion
		ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:29:9-35
	android:name
		ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:28:9-65
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:32:5-76
	android:name
		ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:32:22-73
uses-permission#android.permission.READ_MEDIA_VIDEO
ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:33:5-75
	android:name
		ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:33:22-72
uses-permission#android.permission.READ_MEDIA_AUDIO
ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:34:5-75
	android:name
		ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:34:22-72
uses-permission#android.permission.READ_MEDIA_VISUAL_USER_SELECTED
ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:37:5-90
	android:name
		ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:37:22-87
queries
ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:111:5-128:15
MERGED from [:file_picker] D:\000.Workspace\Lekky\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-15:15
MERGED from [:file_picker] D:\000.Workspace\Lekky\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-15:15
intent#action:name:android.intent.action.PROCESS_TEXT+data:mimeType:text/plain
ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:112:9-115:18
action#android.intent.action.PROCESS_TEXT
ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:113:13-72
	android:name
		ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:113:21-70
data
ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:114:13-50
	android:mimeType
		ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:114:19-48
intent#action:name:android.intent.action.OPEN_DOCUMENT_TREE
ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:117:9-119:18
action#android.intent.action.OPEN_DOCUMENT_TREE
ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:118:13-79
	android:name
		ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:118:21-76
intent#action:name:android.intent.action.CREATE_DOCUMENT+data:mimeType:text/csv
ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:120:9-123:18
action#android.intent.action.CREATE_DOCUMENT
ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:121:13-76
	android:name
		ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:121:21-73
intent#action:name:android.intent.action.OPEN_DOCUMENT+data:mimeType:text/csv
ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:124:9-127:18
action#android.intent.action.OPEN_DOCUMENT
ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:125:13-74
	android:name
		ADDED from D:\000.Workspace\Lekky\android\app\src\main\AndroidManifest.xml:125:21-71
uses-permission#android.permission.INTERNET
ADDED from D:\000.Workspace\Lekky\android\app\src\debug\AndroidManifest.xml:6:5-66
	android:name
		ADDED from D:\000.Workspace\Lekky\android\app\src\debug\AndroidManifest.xml:6:22-64
uses-sdk
INJECTED from D:\000.Workspace\Lekky\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\000.Workspace\Lekky\android\app\src\debug\AndroidManifest.xml
INJECTED from D:\000.Workspace\Lekky\android\app\src\debug\AndroidManifest.xml
MERGED from [:file_picker] D:\000.Workspace\Lekky\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:file_picker] D:\000.Workspace\Lekky\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] D:\000.Workspace\Lekky\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] D:\000.Workspace\Lekky\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:sqflite] D:\000.Workspace\Lekky\build\sqflite\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:sqflite] D:\000.Workspace\Lekky\build\sqflite\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_local_notifications] D:\000.Workspace\Lekky\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_local_notifications] D:\000.Workspace\Lekky\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] D:\000.Workspace\Lekky\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] D:\000.Workspace\Lekky\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] D:\000.Workspace\Lekky\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] D:\000.Workspace\Lekky\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_secure_storage] D:\000.Workspace\Lekky\build\flutter_secure_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-8:53
MERGED from [:flutter_secure_storage] D:\000.Workspace\Lekky\build\flutter_secure_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-8:53
MERGED from [:alarm] D:\000.Workspace\Lekky\build\alarm\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:alarm] D:\000.Workspace\Lekky\build\alarm\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_fgbg] D:\000.Workspace\Lekky\build\flutter_fgbg\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_fgbg] D:\000.Workspace\Lekky\build\flutter_fgbg\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_native_splash] D:\000.Workspace\Lekky\build\flutter_native_splash\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:flutter_native_splash] D:\000.Workspace\Lekky\build\flutter_native_splash\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] D:\000.Workspace\Lekky\build\shared_preferences_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] D:\000.Workspace\Lekky\build\shared_preferences_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] D:\000.Workspace\Lekky\build\permission_handler_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] D:\000.Workspace\Lekky\build\permission_handler_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:integration_test] D:\000.Workspace\Lekky\build\integration_test\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-44
MERGED from [:integration_test] D:\000.Workspace\Lekky\build\integration_test\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-44
MERGED from [:file_saver] D:\000.Workspace\Lekky\build\file_saver\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:file_saver] D:\000.Workspace\Lekky\build\file_saver\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:package_info_plus] D:\000.Workspace\Lekky\build\package_info_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:package_info_plus] D:\000.Workspace\Lekky\build\package_info_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:device_info_plus] D:\000.Workspace\Lekky\build\device_info_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:device_info_plus] D:\000.Workspace\Lekky\build\device_info_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:workmanager] D:\000.Workspace\Lekky\build\workmanager\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:workmanager] D:\000.Workspace\Lekky\build\workmanager\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [androidx.window:window-java:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cdacfaa49b43f0b8e5751d92c0b5e4a8\transformed\jetified-window-java-1.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [androidx.window:window-java:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\cdacfaa49b43f0b8e5751d92c0b5e4a8\transformed\jetified-window-java-1.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\76e7c0e46e1e41ca65da1405b9eb1cd5\transformed\jetified-window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\76e7c0e46e1e41ca65da1405b9eb1cd5\transformed\jetified-window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\33e7cd33ba25126286e7a29ce6645aa9\transformed\material-1.9.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\33e7cd33ba25126286e7a29ce6645aa9\transformed\material-1.9.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.play:app-update:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\767dc9d8c1dfe9a3759a834576eb57d7\transformed\jetified-app-update-2.1.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.play:app-update:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\767dc9d8c1dfe9a3759a834576eb57d7\transformed\jetified-app-update-2.1.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.play:feature-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\48e5595d725ffd7a1c5cbadee2d24a51\transformed\jetified-feature-delivery-2.1.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.play:feature-delivery:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\48e5595d725ffd7a1c5cbadee2d24a51\transformed\jetified-feature-delivery-2.1.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\b69303ac21562a827ede65f286c3b864\transformed\jetified-play-services-tasks-18.0.2\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\b69303ac21562a827ede65f286c3b864\transformed\jetified-play-services-tasks-18.0.2\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\fa41a8f4d43ebdb1a2bdc7a1d2c6fe82\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\fa41a8f4d43ebdb1a2bdc7a1d2c6fe82\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79b3b600fb3b916d065f9ce1405580fd\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\79b3b600fb3b916d065f9ce1405580fd\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\e4725ad2ec266c1f3d48d9d3d3128614\transformed\jetified-appcompat-resources-1.5.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\e4725ad2ec266c1f3d48d9d3d3128614\transformed\jetified-appcompat-resources-1.5.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\59b512fe865bec571721add8e6d44c7d\transformed\appcompat-1.5.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\59b512fe865bec571721add8e6d44c7d\transformed\appcompat-1.5.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0bdc6b6c00c70c057f86cfee33dfeb3f\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0bdc6b6c00c70c057f86cfee33dfeb3f\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-4\30815b1d4166c0c0244953ad3928dd40\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-4\30815b1d4166c0c0244953ad3928dd40\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\7a5cf8f2ab36e18f9cb80ea2e0cdc1a7\transformed\jetified-activity-1.5.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\7a5cf8f2ab36e18f9cb80ea2e0cdc1a7\transformed\jetified-activity-1.5.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7cc8374f835c9c1166e236439af6dc56\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7cc8374f835c9c1166e236439af6dc56\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\65f154d63c15b7fc44b1e2c67366acc9\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\65f154d63c15b7fc44b1e2c67366acc9\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b11131fe164bea9d1c2ba190d8dcd286\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b11131fe164bea9d1c2ba190d8dcd286\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\b5708d878f9c65cbfc390dbd179742d7\transformed\lifecycle-viewmodel-2.5.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\b5708d878f9c65cbfc390dbd179742d7\transformed\lifecycle-viewmodel-2.5.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\66e24abd468c391ce79e1be0c39e41e1\transformed\jetified-lifecycle-viewmodel-savedstate-2.5.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\66e24abd468c391ce79e1be0c39e41e1\transformed\jetified-lifecycle-viewmodel-savedstate-2.5.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\ffce1bb1ba7d6308e78ae0ff3994d469\transformed\room-runtime-2.5.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\ffce1bb1ba7d6308e78ae0ff3994d469\transformed\room-runtime-2.5.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.media:media:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\cb77ff9286007a8143de4cd38a9909a4\transformed\media-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\cb77ff9286007a8143de4cd38a9909a4\transformed\media-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\b6a20bfcf076cfb90273b0a64bffb348\transformed\browser-1.5.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.browser:browser:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\b6a20bfcf076cfb90273b0a64bffb348\transformed\browser-1.5.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\07354d9cdbbea0612e8a514a4fce0df0\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\07354d9cdbbea0612e8a514a4fce0df0\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\ede0346f692b4c87bee695987057451d\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\ede0346f692b4c87bee695987057451d\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\e64bb8ba5ceca538c85fb9a0930d16fd\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\e64bb8ba5ceca538c85fb9a0930d16fd\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\7c3148cbc4d7fe4febd157ae6b3ae3db\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\7c3148cbc4d7fe4febd157ae6b3ae3db\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\67fef3903dc7dfdb2273c51ce67a409c\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\67fef3903dc7dfdb2273c51ce67a409c\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\2be508f60f4457696ca8937cb2b9db31\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\2be508f60f4457696ca8937cb2b9db31\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\63063b26a8d5be2fb0fed04707a2e278\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\63063b26a8d5be2fb0fed04707a2e278\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\a88c8b481acb52f3a4bfeb2374d5377d\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\a88c8b481acb52f3a4bfeb2374d5377d\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fd461721109da9d2cd57f84f9f45fd20\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fd461721109da9d2cd57f84f9f45fd20\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\dffc091555c1f9431e93e40c2323fcda\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\dffc091555c1f9431e93e40c2323fcda\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-4\28e62e3ce1c18f22838066d155aeee90\transformed\jetified-core-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-4\28e62e3ce1c18f22838066d155aeee90\transformed\jetified-core-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-4\6e1dc6c49b9b4e7630c6b57963983704\transformed\core-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-4\6e1dc6c49b9b4e7630c6b57963983704\transformed\core-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\transforms-4\ade3d854e511a64ccff7f22bf0e12aba\transformed\jetified-security-crypto-1.1.0-alpha06\AndroidManifest.xml:20:5-44
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\transforms-4\ade3d854e511a64ccff7f22bf0e12aba\transformed\jetified-security-crypto-1.1.0-alpha06\AndroidManifest.xml:20:5-44
MERGED from [androidx.test:rules:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\8079e3865619c68467fdf1a6e7e7c912\transformed\rules-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:rules:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\8079e3865619c68467fdf1a6e7e7c912\transformed\rules-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test.espresso:espresso-core:3.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\377c7c77789a01bb5e5fe9bf8f7b34ef\transformed\espresso-core-3.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test.espresso:espresso-core:3.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\377c7c77789a01bb5e5fe9bf8f7b34ef\transformed\espresso-core-3.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:runner:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\d74b85b36d30dc1a8e0e0046cb52ed04\transformed\runner-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:runner:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\d74b85b36d30dc1a8e0e0046cb52ed04\transformed\runner-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\2a5f274ded50dacc86153bab5a91cc40\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\2a5f274ded50dacc86153bab5a91cc40\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a076ace06b5c080e326a1aff8e74515\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a076ace06b5c080e326a1aff8e74515\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\1758dd11e3be0aa50b0e6559e83f82a9\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\1758dd11e3be0aa50b0e6559e83f82a9\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\c48568960db0ea95ff408bd73d1cd09a\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\c48568960db0ea95ff408bd73d1cd09a\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\1375740d2c2cf2a30d23306e1504e17c\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\1375740d2c2cf2a30d23306e1504e17c\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6431675dbcebd02667ecbfc027fe6f1e\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6431675dbcebd02667ecbfc027fe6f1e\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.test:monitor:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\ec6270dd602c2300d34c6facdfd3a1d8\transformed\monitor-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:monitor:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\ec6270dd602c2300d34c6facdfd3a1d8\transformed\monitor-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\66fb3d43cb3a86f46934dd953cf635f7\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\66fb3d43cb3a86f46934dd953cf635f7\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0a6801872af18637c49e16ca97c54755\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0a6801872af18637c49e16ca97c54755\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\c2d243d8236fa183efe93838b9ca306a\transformed\lifecycle-livedata-2.1.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\c2d243d8236fa183efe93838b9ca306a\transformed\lifecycle-livedata-2.1.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\dc99ecf8f602463761a4be706db32bc0\transformed\jetified-lifecycle-service-2.1.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\dc99ecf8f602463761a4be706db32bc0\transformed\jetified-lifecycle-service-2.1.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\4e58d9cfab31179d3452d400b573a90e\transformed\lifecycle-runtime-2.5.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\4e58d9cfab31179d3452d400b573a90e\transformed\lifecycle-runtime-2.5.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\31e85674cdac0fc0b61ff2b7afb694e2\transformed\lifecycle-livedata-core-2.5.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\31e85674cdac0fc0b61ff2b7afb694e2\transformed\lifecycle-livedata-core-2.5.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\7e2b8d11ccff5fcd582a4ed44f03aade\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\7e2b8d11ccff5fcd582a4ed44f03aade\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\779bc8cf66cf76ffbd6b861b4274d665\transformed\jetified-savedstate-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\779bc8cf66cf76ffbd6b861b4274d665\transformed\jetified-savedstate-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ee5c1ccb835017032ade377a5d2be3eb\transformed\sqlite-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ee5c1ccb835017032ade377a5d2be3eb\transformed\sqlite-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0398ad951430f62fa74fbbd37bace3b0\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\0398ad951430f62fa74fbbd37bace3b0\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\287570b0df0c27a7af4fe4bca0025a11\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\287570b0df0c27a7af4fe4bca0025a11\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\31b0028aec0dcade7346db1bf86855eb\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\31b0028aec0dcade7346db1bf86855eb\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\09304b40a2bfb0f9741f03fa0a5319f2\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\09304b40a2bfb0f9741f03fa0a5319f2\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\ce88c44a4a968999ee4b64dbbb01246d\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\ce88c44a4a968999ee4b64dbbb01246d\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.test.espresso:espresso-idling-resource:3.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\6f73da4199dfd93b79ec2a11e3749369\transformed\espresso-idling-resource-3.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test.espresso:espresso-idling-resource:3.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\6f73da4199dfd93b79ec2a11e3749369\transformed\espresso-idling-resource-3.2.0\AndroidManifest.xml:20:5-22:41
	tools:overrideLibrary
		ADDED from [:flutter_secure_storage] D:\000.Workspace\Lekky\build\flutter_secure_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-50
	android:targetSdkVersion
		INJECTED from D:\000.Workspace\Lekky\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\000.Workspace\Lekky\android\app\src\debug\AndroidManifest.xml
intent#action:name:android.intent.action.GET_CONTENT+data:mimeType:*/*
ADDED from [:file_picker] D:\000.Workspace\Lekky\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:9-14:18
action#android.intent.action.GET_CONTENT
ADDED from [:file_picker] D:\000.Workspace\Lekky\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-72
	android:name
		ADDED from [:file_picker] D:\000.Workspace\Lekky\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:21-69
activity#io.flutter.plugins.urllauncher.WebViewActivity
ADDED from [:url_launcher_android] D:\000.Workspace\Lekky\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
	android:exported
		ADDED from [:url_launcher_android] D:\000.Workspace\Lekky\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [:url_launcher_android] D:\000.Workspace\Lekky\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
	android:name
		ADDED from [:url_launcher_android] D:\000.Workspace\Lekky\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
receiver#com.gdelataillade.alarm.alarm.AlarmReceiver
MERGED from [:alarm] D:\000.Workspace\Lekky\build\alarm\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:9-80
MERGED from [:alarm] D:\000.Workspace\Lekky\build\alarm\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:9-80
service#com.gdelataillade.alarm.alarm.AlarmService
MERGED from [:alarm] D:\000.Workspace\Lekky\build\alarm\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:9-26:19
MERGED from [:alarm] D:\000.Workspace\Lekky\build\alarm\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:9-26:19
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\76e7c0e46e1e41ca65da1405b9eb1cd5\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\76e7c0e46e1e41ca65da1405b9eb1cd5\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\76e7c0e46e1e41ca65da1405b9eb1cd5\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\76e7c0e46e1e41ca65da1405b9eb1cd5\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\76e7c0e46e1e41ca65da1405b9eb1cd5\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\76e7c0e46e1e41ca65da1405b9eb1cd5\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\fa41a8f4d43ebdb1a2bdc7a1d2c6fe82\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\fa41a8f4d43ebdb1a2bdc7a1d2c6fe82\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\fa41a8f4d43ebdb1a2bdc7a1d2c6fe82\transformed\jetified-play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:24:5-79
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:24:22-76
provider#androidx.startup.InitializationProvider
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\63063b26a8d5be2fb0fed04707a2e278\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\63063b26a8d5be2fb0fed04707a2e278\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\1758dd11e3be0aa50b0e6559e83f82a9\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:26:9-34:20
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\1758dd11e3be0aa50b0e6559e83f82a9\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:26:9-34:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\c48568960db0ea95ff408bd73d1cd09a\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\c48568960db0ea95ff408bd73d1cd09a\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:33:13-31
	android:authorities
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:31:13-68
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:30:13-67
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
	android:value
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:36:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:35:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:117:25-76
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ef864d2df2ca98972818d8e9c089cff\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\ffce1bb1ba7d6308e78ae0ff3994d469\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\ffce1bb1ba7d6308e78ae0ff3994d469\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\ffce1bb1ba7d6308e78ae0ff3994d469\transformed\room-runtime-2.5.0\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\ffce1bb1ba7d6308e78ae0ff3994d469\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\ffce1bb1ba7d6308e78ae0ff3994d469\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\63063b26a8d5be2fb0fed04707a2e278\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\63063b26a8d5be2fb0fed04707a2e278\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\63063b26a8d5be2fb0fed04707a2e278\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-4\6e1dc6c49b9b4e7630c6b57963983704\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-4\6e1dc6c49b9b4e7630c6b57963983704\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-4\6e1dc6c49b9b4e7630c6b57963983704\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
permission#com.lekky.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-4\6e1dc6c49b9b4e7630c6b57963983704\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-4\6e1dc6c49b9b4e7630c6b57963983704\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-4\6e1dc6c49b9b4e7630c6b57963983704\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-4\6e1dc6c49b9b4e7630c6b57963983704\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-4\6e1dc6c49b9b4e7630c6b57963983704\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
uses-permission#com.lekky.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-4\6e1dc6c49b9b4e7630c6b57963983704\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\transforms-4\6e1dc6c49b9b4e7630c6b57963983704\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\1758dd11e3be0aa50b0e6559e83f82a9\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\1758dd11e3be0aa50b0e6559e83f82a9\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\1758dd11e3be0aa50b0e6559e83f82a9\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78
activity#com.google.android.play.core.common.PlayCoreDialogWrapperActivity
ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\ce88c44a4a968999ee4b64dbbb01246d\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:14:9-18:65
	android:stateNotNeeded
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\ce88c44a4a968999ee4b64dbbb01246d\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:17:13-42
	android:exported
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\ce88c44a4a968999ee4b64dbbb01246d\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:16:13-37
	android:theme
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\ce88c44a4a968999ee4b64dbbb01246d\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:18:13-62
	android:name
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-4\ce88c44a4a968999ee4b64dbbb01246d\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:15:13-93
