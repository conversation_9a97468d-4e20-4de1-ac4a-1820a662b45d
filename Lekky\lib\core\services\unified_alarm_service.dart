import 'dart:async';
import 'dart:io';
import 'package:alarm/alarm.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/logger.dart';
import '../utils/event_bus.dart';
import '../utils/average_calculator.dart';
import '../constants/preference_keys.dart';
import '../di/service_locator.dart';
import '../../features/averages/domain/services/average_service.dart';
import '../../features/meter_readings/domain/repositories/meter_reading_repository.dart';
import '../../features/top_ups/domain/repositories/top_up_repository.dart';
import '../../features/notifications/domain/models/notification.dart';

/// Unified service for managing all alarm types using the alarm package
/// Handles meter reading reminders, threshold alerts, and low balance alerts
class UnifiedAlarmService {
  static final UnifiedAlarmService _instance = UnifiedAlarmService._internal();
  factory UnifiedAlarmService() => _instance;
  UnifiedAlarmService._internal();

  // Alarm ID constants
  static const int meterReadingReminderId = 1003;
  static const int lowBalanceAlertId = 1101;
  static const int thresholdAlertId = 1102;

  StreamSubscription<AlarmSettings>? _alarmSubscription;
  StreamSubscription<EventType>? _eventSubscription;
  bool _isInitialized = false;

  // Debouncing for reminder scheduling to prevent duplicates
  Timer? _reminderSchedulingTimer;

  /// Initialize the unified alarm service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      Logger.info('UnifiedAlarmService: Initializing...');

      // Set up alarm stream listener
      _setupAlarmListener();

      // Set up event bus listener
      _setupEventListener();

      _isInitialized = true;
      Logger.info('UnifiedAlarmService: Initialized successfully');
    } catch (e) {
      Logger.error('UnifiedAlarmService: Initialization failed: $e');
      rethrow;
    }
  }

  /// Set up alarm stream listener to handle all alarm types
  void _setupAlarmListener() {
    try {
      Logger.info(
          'UnifiedAlarmService: 🎧 Setting up alarm stream listener...');
      _alarmSubscription?.cancel();
      Logger.info(
          'UnifiedAlarmService: 🔧 DEBUG: About to listen to Alarm.ringStream.stream');
      _alarmSubscription = Alarm.ringStream.stream.listen((alarmSettings) {
        Logger.info(
            'UnifiedAlarmService: 🔔 ALARM FIRED WITH ID: ${alarmSettings.id}');

        switch (alarmSettings.id) {
          case meterReadingReminderId:
            Logger.info(
                'UnifiedAlarmService: 📅 Handling meter reading reminder');
            _handleMeterReadingReminder(alarmSettings);
            break;
          case lowBalanceAlertId:
            Logger.info('UnifiedAlarmService: 🔋 Handling low balance alert');
            _handleLowBalanceAlert(alarmSettings);
            break;
          case thresholdAlertId:
            Logger.info('UnifiedAlarmService: 💰 Handling threshold alert');
            _handleThresholdAlert(alarmSettings);
            break;
          default:
            Logger.info(
                'UnifiedAlarmService: ❓ Ignoring alarm with unknown ID: ${alarmSettings.id}');
        }
      });
      Logger.info(
          'UnifiedAlarmService: ✅ Alarm stream listener set up successfully');
    } catch (e) {
      Logger.error(
          'UnifiedAlarmService: ❌ ERROR setting up alarm stream listener: $e');
      rethrow;
    }
  }

  /// Set up event bus listener for settings changes
  void _setupEventListener() {
    _eventSubscription?.cancel();
    _eventSubscription = EventBus().stream.listen((event) {
      Logger.info('UnifiedAlarmService: 🔥 EventBus event received: $event');
      switch (event) {
        case EventType.reminderSettingsUpdated:
          Logger.info(
              'UnifiedAlarmService: 📅 Processing reminderSettingsUpdated event');
          _handleReminderSettingsUpdate();
          break;
        case EventType.alertSettingsUpdated:
          Logger.info(
              'UnifiedAlarmService: 🚨 Processing alertSettingsUpdated event');
          _handleAlertSettingsUpdate();
          break;
        case EventType.dataUpdated:
          Logger.info('UnifiedAlarmService: 📊 Processing dataUpdated event');
          _handleDashboardDataUpdate();
          break;
        default:
          Logger.info('UnifiedAlarmService: ❓ Ignoring event: $event');
          break;
      }
    });
    Logger.info(
        'UnifiedAlarmService: 🎧 EventBus listener set up successfully');
  }

  /// Handle meter reading reminder alarm
  void _handleMeterReadingReminder(AlarmSettings alarmSettings) async {
    try {
      Logger.info('UnifiedAlarmService: Handling meter reading reminder');

      // CRITICAL: Stop the alarm IMMEDIATELY to prevent auto-rescheduling by alarm package
      await Alarm.stop(meterReadingReminderId);
      Logger.info(
          'UnifiedAlarmService: 🛑 Stopped alarm to prevent auto-rescheduling');

      // Trigger reminder dialog via EventBus (SimpleReminderService will handle the UI)
      EventBus().fireReminderDialog(ReminderDialogEventData(
        alarmSettings: alarmSettings,
      ));

      // Create in-app notification
      await _createInAppNotification(AppNotification(
        title: 'Meter Reading Reminder',
        message: 'Time to check your electricity meter',
        timestamp: DateTime.now(),
        type: NotificationType.readingReminder,
      ));

      // Schedule next reminder for NEXT occurrence (not current time)
      await _scheduleNextMeterReadingReminderForced();
      Logger.info(
          'UnifiedAlarmService: ✅ Next reminder scheduled successfully');

      Logger.info(
          'UnifiedAlarmService: Meter reading reminder handled successfully');
    } catch (e) {
      Logger.error(
          'UnifiedAlarmService: Error handling meter reading reminder: $e');
    }
  }

  /// Handle low balance alert alarm
  void _handleLowBalanceAlert(AlarmSettings alarmSettings) async {
    try {
      Logger.info('UnifiedAlarmService: Handling low balance alert');

      // Stop the alarm after 7 repeats (limited duration)
      Timer(const Duration(seconds: 21), () async {
        await Alarm.stop(lowBalanceAlertId);
        Logger.info(
            'UnifiedAlarmService: Auto-stopped low balance alert after 21 seconds');
      });

      // Create in-app notification with the same message as the alarm
      await _createInAppNotification(AppNotification(
        title: alarmSettings.notificationTitle,
        message: alarmSettings.notificationBody,
        timestamp: DateTime.now(),
        type: NotificationType.lowBalance,
      ));

      // Schedule next check in 6 hours
      await _scheduleNextLowBalanceCheck();
    } catch (e) {
      Logger.error('UnifiedAlarmService: Error handling low balance alert: $e');
    }
  }

  /// Handle threshold alert alarm
  void _handleThresholdAlert(AlarmSettings alarmSettings) async {
    try {
      Logger.info('UnifiedAlarmService: Handling threshold alert');

      // Stop the alarm after 7 repeats (limited duration)
      Timer(const Duration(seconds: 21), () async {
        await Alarm.stop(thresholdAlertId);
        Logger.info(
            'UnifiedAlarmService: Auto-stopped threshold alert after 21 seconds');
      });

      // Create in-app notification with the same message as the alarm
      await _createInAppNotification(AppNotification(
        title: alarmSettings.notificationTitle,
        message: alarmSettings.notificationBody,
        timestamp: DateTime.now(),
        type: NotificationType.timeToTopUp,
      ));

      // Schedule next check in 6 hours
      await _scheduleNextThresholdCheck();
    } catch (e) {
      Logger.error('UnifiedAlarmService: Error handling threshold alert: $e');
    }
  }

  /// Create in-app notification through notification service
  Future<void> _createInAppNotification(AppNotification notification) async {
    try {
      // Fire event to trigger immediate provider refresh
      EventBus().fire(EventType.notificationCreated);
      Logger.info(
          'UnifiedAlarmService: Created in-app notification: ${notification.title}');
    } catch (e) {
      Logger.error(
          'UnifiedAlarmService: Error creating in-app notification: $e');
    }
  }

  /// Handle reminder settings update with debouncing to prevent duplicates
  void _handleReminderSettingsUpdate() async {
    try {
      Logger.info('UnifiedAlarmService: Handling reminder settings update');

      // Cancel any existing timer to debounce rapid events
      _reminderSchedulingTimer?.cancel();

      // Schedule the reminder update after a short delay to debounce multiple events
      _reminderSchedulingTimer =
          Timer(const Duration(milliseconds: 500), () async {
        try {
          await _scheduleNextMeterReadingReminder();
        } catch (e) {
          Logger.error(
              'UnifiedAlarmService: Error in debounced reminder scheduling: $e');
        }
      });
    } catch (e) {
      Logger.error(
          'UnifiedAlarmService: Error handling reminder settings update: $e');
    }
  }

  /// Handle alert settings update
  void _handleAlertSettingsUpdate() async {
    try {
      Logger.info('UnifiedAlarmService: Handling alert settings update');
      await _rescheduleAllAlerts();
    } catch (e) {
      Logger.error(
          'UnifiedAlarmService: Error handling alert settings update: $e');
    }
  }

  /// Handle dashboard data update
  void _handleDashboardDataUpdate() async {
    try {
      Logger.info('UnifiedAlarmService: Handling dashboard data update');
      await _rescheduleAllAlerts();
    } catch (e) {
      Logger.error(
          'UnifiedAlarmService: Error handling dashboard data update: $e');
    }
  }

  /// Schedule next meter reading reminder
  Future<void> _scheduleNextMeterReadingReminder() async {
    try {
      Logger.info(
          'UnifiedAlarmService: 🚀 _scheduleNextMeterReadingReminder() called');
      Logger.info('UnifiedAlarmService: 📍 Call stack: ${StackTrace.current}');

      final prefs = await SharedPreferences.getInstance();
      final reminderEnabled =
          prefs.getBool(PreferenceKeys.remindersEnabled) ?? false;

      Logger.info(
          'UnifiedAlarmService: DEBUG - reminderEnabled: $reminderEnabled');

      if (!reminderEnabled) {
        await Alarm.stop(meterReadingReminderId);
        Logger.info(
            'UnifiedAlarmService: Meter reading reminders disabled, stopped alarm');
        return;
      }

      final reminderFrequency =
          prefs.getString(PreferenceKeys.reminderFrequency) ?? 'weekly';
      final reminderStartDateTime =
          prefs.getString(PreferenceKeys.reminderStartDateTime);

      Logger.info(
          'UnifiedAlarmService: DEBUG - reminderFrequency: $reminderFrequency');
      Logger.info(
          'UnifiedAlarmService: DEBUG - reminderStartDateTime: $reminderStartDateTime');

      if (reminderStartDateTime == null) {
        Logger.warning('UnifiedAlarmService: No reminder start time set');
        return;
      }

      final startTime = DateTime.parse(reminderStartDateTime);
      final nextReminderTime =
          _calculateNextReminderTime(startTime, reminderFrequency);

      await _scheduleAlarm(
        id: meterReadingReminderId,
        dateTime: nextReminderTime,
        title: 'Meter Reading Reminder',
        body:
            'Time to check your electricity meter - Tap to dismiss or add reading',
        loopAudio:
            false, // CRITICAL: Disable looping to prevent auto-rescheduling
        androidFullScreenIntent: true,
      );

      Logger.info(
          'UnifiedAlarmService: Scheduled next meter reading reminder for: $nextReminderTime');
    } catch (e) {
      Logger.error(
          'UnifiedAlarmService: Error scheduling meter reading reminder: $e');
    }
  }

  /// Force reschedule for NEXT occurrence after alarm has fired
  Future<void> _scheduleNextMeterReadingReminderForced() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final reminderEnabled =
          prefs.getBool(PreferenceKeys.remindersEnabled) ?? false;

      if (!reminderEnabled) {
        await Alarm.stop(meterReadingReminderId);
        Logger.info(
            'UnifiedAlarmService: Meter reading reminders disabled, stopped alarm');
        return;
      }

      final reminderFrequency =
          prefs.getString(PreferenceKeys.reminderFrequency) ?? 'weekly';
      final reminderStartDateTime =
          prefs.getString(PreferenceKeys.reminderStartDateTime);

      if (reminderStartDateTime == null) {
        Logger.warning('UnifiedAlarmService: No reminder start time set');
        return;
      }

      final startTime = DateTime.parse(reminderStartDateTime);

      // Force calculation from current time to ensure NEXT occurrence
      final now = DateTime.now();
      final nextReminderTime = _calculateNextReminderTime(
          startTime, reminderFrequency,
          fromTime: now);

      await _scheduleAlarm(
        id: meterReadingReminderId,
        dateTime: nextReminderTime,
        title: 'Meter Reading Reminder',
        body:
            'Time to check your electricity meter - Tap to dismiss or add reading',
        loopAudio:
            false, // CRITICAL: Disable looping to prevent auto-rescheduling
        androidFullScreenIntent: true,
      );

      Logger.info(
          'UnifiedAlarmService: FORCED next reminder scheduled for $nextReminderTime');
    } catch (e) {
      Logger.error(
          'UnifiedAlarmService: Error in forced reminder scheduling: $e');
    }
  }

  /// Schedule next low balance check
  Future<void> _scheduleNextLowBalanceCheck() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lowBalanceEnabled =
          prefs.getBool(PreferenceKeys.lowBalanceAlertsEnabled) ?? false;

      if (!lowBalanceEnabled) {
        await Alarm.stop(lowBalanceAlertId);
        Logger.info(
            'UnifiedAlarmService: Low balance alerts disabled, stopped alarm');
        return;
      }

      // Schedule next check in 6 hours for background monitoring
      final nextCheckTime = DateTime.now().add(const Duration(hours: 6));

      await _scheduleAlarm(
        id: lowBalanceAlertId,
        dateTime: nextCheckTime,
        title: 'Low Balance Alert',
        body: 'Your meter balance is low',
        loopAudio: false,
        androidFullScreenIntent: false,
      );

      Logger.info(
          'UnifiedAlarmService: Scheduled next low balance check for: $nextCheckTime');
    } catch (e) {
      Logger.error(
          'UnifiedAlarmService: Error scheduling low balance check: $e');
    }
  }

  /// Schedule next threshold check
  Future<void> _scheduleNextThresholdCheck() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final thresholdEnabled =
          prefs.getBool(PreferenceKeys.timeToTopUpAlertsEnabled) ?? false;

      if (!thresholdEnabled) {
        await Alarm.stop(thresholdAlertId);
        Logger.info(
            'UnifiedAlarmService: Threshold alerts disabled, stopped alarm');
        return;
      }

      // Schedule next check in 6 hours for background monitoring
      final nextCheckTime = DateTime.now().add(const Duration(hours: 6));

      await _scheduleAlarm(
        id: thresholdAlertId,
        dateTime: nextCheckTime,
        title: 'Time to Top-Up',
        body: 'You should consider topping up your meter soon',
        loopAudio: false,
        androidFullScreenIntent: false,
      );

      Logger.info(
          'UnifiedAlarmService: Scheduled next threshold check for: $nextCheckTime');
    } catch (e) {
      Logger.error('UnifiedAlarmService: Error scheduling threshold check: $e');
    }
  }

  /// Reschedule all alerts based on current data
  Future<void> _rescheduleAllAlerts() async {
    try {
      Logger.info('UnifiedAlarmService: Rescheduling all alerts');

      // Cancel existing alarms
      await Alarm.stop(lowBalanceAlertId);
      await Alarm.stop(thresholdAlertId);

      // Reschedule based on current conditions and predictions
      await _predictAndScheduleAlerts();

      Logger.info('UnifiedAlarmService: All alerts rescheduled');
    } catch (e) {
      Logger.error('UnifiedAlarmService: Error rescheduling alerts: $e');
    }
  }

  /// Predict and schedule alerts based on current usage patterns
  Future<void> _predictAndScheduleAlerts() async {
    try {
      Logger.info('UnifiedAlarmService: Predicting and scheduling alerts');

      final prefs = await SharedPreferences.getInstance();

      // Check if alerts are enabled
      final lowBalanceEnabled =
          prefs.getBool(PreferenceKeys.lowBalanceAlertsEnabled) ?? false;
      final thresholdEnabled =
          prefs.getBool(PreferenceKeys.timeToTopUpAlertsEnabled) ?? false;

      if (!lowBalanceEnabled && !thresholdEnabled) {
        Logger.info(
            'UnifiedAlarmService: All alerts disabled, skipping prediction');
        return;
      }

      // Get dashboard data for calculations
      final dashboardData = await _getDashboardData();
      if (dashboardData == null) {
        Logger.warning(
            'UnifiedAlarmService: No dashboard data available for prediction');
        // Fall back to regular checks
        if (lowBalanceEnabled) await _scheduleNextLowBalanceCheck();
        if (thresholdEnabled) await _scheduleNextThresholdCheck();
        return;
      }

      // Predict low balance alert timing
      if (lowBalanceEnabled) {
        await _predictAndScheduleLowBalanceAlert(dashboardData);
      }

      // Predict threshold alert timing
      if (thresholdEnabled) {
        await _predictAndScheduleThresholdAlert(dashboardData);
      }
    } catch (e) {
      Logger.error('UnifiedAlarmService: Error predicting alerts: $e');
      // Fall back to regular checks
      await _scheduleNextLowBalanceCheck();
      await _scheduleNextThresholdCheck();
    }
  }

  /// Get dashboard data for predictions using real-time data
  Future<Map<String, dynamic>?> _getDashboardData() async {
    try {
      final meterReadingRepo = serviceLocator<MeterReadingRepository>();
      final topUpRepo = serviceLocator<TopUpRepository>();
      final averageService = serviceLocator<AverageService>();

      // Get latest meter reading
      final latestMeterReading = await meterReadingRepo.getLatestMeterReading();
      if (latestMeterReading == null) {
        Logger.warning('UnifiedAlarmService: No meter reading available');
        return null;
      }

      // Get averages
      final averageResult = await averageService.getAverages();
      final dailyUsage =
          averageResult.recentAverage ?? averageResult.totalAverage;

      if (dailyUsage == null || dailyUsage <= 0) {
        Logger.warning('UnifiedAlarmService: No valid daily usage data');
        return null;
      }

      // Calculate top-ups after latest reading
      final topUps = await topUpRepo.getTopUpsByDateRange(
        startDate: latestMeterReading.date,
        endDate: DateTime.now(),
      );
      final topUpsAfterLatest = topUps
          .where((topUp) => topUp.date.isAfter(latestMeterReading.date))
          .fold<double>(0.0, (sum, topUp) => sum + topUp.amount);

      // Calculate current balance
      final currentBalance = latestMeterReading.value + topUpsAfterLatest;

      Logger.info(
          'UnifiedAlarmService: Dashboard data - Balance: $currentBalance, Daily Usage: $dailyUsage');

      return {
        'currentBalance': currentBalance,
        'dailyUsage': dailyUsage,
        'hasValidData': true,
      };
    } catch (e) {
      Logger.error('UnifiedAlarmService: Error getting dashboard data: $e');
      return null;
    }
  }

  /// Predict and schedule low balance alert
  Future<void> _predictAndScheduleLowBalanceAlert(
      Map<String, dynamic> dashboardData) async {
    try {
      // Calculate when meter will reach zero
      final daysToZero = _calculateDaysToMeterZero(dashboardData);

      if (daysToZero == null) {
        Logger.info(
            'UnifiedAlarmService: No balance data available, scheduling fallback check');
        await _scheduleNextLowBalanceCheck();
        return;
      }

      if (daysToZero <= 0) {
        Logger.info(
            'UnifiedAlarmService: Meter already at zero, firing immediate critical alert');
        // Fire immediate alert for critical situation
        _handleLowBalanceAlert(AlarmSettings(
          id: lowBalanceAlertId,
          dateTime: DateTime.now(),
          assetAudioPath: 'assets/alarm_short.mp3',
          notificationTitle: 'URGENT: Meter Empty',
          notificationBody: 'Your meter balance has reached zero',
        ));
        return;
      }

      if (daysToZero <= 1.0) {
        final hoursRemaining = (daysToZero * 24).round();
        Logger.info(
            'UnifiedAlarmService: Within 24 hours of zero meter ($hoursRemaining hours) - firing immediate low balance alert');
        // Fire immediate alert - this is the 24-hour warning
        _handleLowBalanceAlert(AlarmSettings(
          id: lowBalanceAlertId,
          dateTime: DateTime.now(),
          assetAudioPath: 'assets/alarm_short.mp3',
          notificationTitle: 'Low Balance Alert',
          notificationBody: hoursRemaining > 0
              ? 'Your meter will reach zero in approximately $hoursRemaining hours'
              : 'Your meter balance is critically low',
        ));
        return;
      }

      // Schedule alert for 24 hours before meter reaches zero
      final alertTime = DateTime.now().add(Duration(
        hours: ((daysToZero - 1.0) * 24)
            .round()
            .clamp(1, 24 * 7), // Max 7 days ahead
      ));

      await _scheduleAlarm(
        id: lowBalanceAlertId,
        dateTime: alertTime,
        title: 'Low Balance Alert',
        body: 'Your meter will reach zero in approximately 24 hours',
        loopAudio: false,
        androidFullScreenIntent: false,
      );

      Logger.info(
          'UnifiedAlarmService: Predicted low balance alert scheduled for: $alertTime (${daysToZero.toStringAsFixed(1)} days to zero)');
    } catch (e) {
      Logger.error(
          'UnifiedAlarmService: Error predicting low balance alert: $e');
      await _scheduleNextLowBalanceCheck();
    }
  }

  /// Predict and schedule threshold alert
  Future<void> _predictAndScheduleThresholdAlert(
      Map<String, dynamic> dashboardData) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final alertThreshold =
          prefs.getDouble(PreferenceKeys.alertThreshold) ?? 5.0;
      final daysInAdvance = prefs.getInt(PreferenceKeys.daysInAdvance) ?? 5;

      // Calculate when meter will reach threshold
      final daysToThreshold = _calculateDaysToThreshold(
          dashboardData, alertThreshold, daysInAdvance);

      if (daysToThreshold == null) {
        Logger.info(
            'UnifiedAlarmService: No threshold data available, scheduling fallback check');
        await _scheduleNextThresholdCheck();
        return;
      }

      if (daysToThreshold <= 0) {
        Logger.info(
            'UnifiedAlarmService: Threshold already reached, firing immediate threshold alert');
        // Fire immediate threshold alert (NOT low balance alert)
        _handleThresholdAlert(AlarmSettings(
          id: thresholdAlertId,
          dateTime: DateTime.now(),
          assetAudioPath: 'assets/alarm_short.mp3',
          notificationTitle: 'Time to Top-Up',
          notificationBody:
              'Your meter balance has reached the alert threshold',
        ));
        return;
      }

      // Schedule alert for when threshold will be reached
      final alertTime = DateTime.now().add(Duration(
        hours: (daysToThreshold * 24)
            .round()
            .clamp(1, 24 * 30), // Max 30 days ahead
      ));

      await _scheduleAlarm(
        id: thresholdAlertId,
        dateTime: alertTime,
        title: 'Time to Top-Up',
        body: 'You should consider topping up your meter soon',
        loopAudio: false,
        androidFullScreenIntent: false,
      );

      Logger.info(
          'UnifiedAlarmService: Predicted threshold alert scheduled for: $alertTime (${daysToThreshold.toStringAsFixed(1)} days to threshold)');
    } catch (e) {
      Logger.error('UnifiedAlarmService: Error predicting threshold alert: $e');
      await _scheduleNextThresholdCheck();
    }
  }

  /// Calculate days until meter reaches zero using Dashboard's calculation method
  double? _calculateDaysToMeterZero(Map<String, dynamic> dashboardData) {
    try {
      final lastMeterReading = dashboardData['lastMeterReading'] as double?;
      final topUpsSinceLastReading =
          dashboardData['topUpsSinceLastReading'] as double?;
      final lastReadingDate = dashboardData['lastReadingDate'] as DateTime?;
      final dailyUsage = dashboardData['dailyUsage'] as double?;

      if (lastMeterReading == null ||
          topUpsSinceLastReading == null ||
          lastReadingDate == null ||
          dailyUsage == null ||
          dailyUsage <= 0) {
        return null;
      }

      // Use AverageCalculator for consistent calculation across the app
      return AverageCalculator.calculateDaysToMeterZero(
        lastMeterReading: lastMeterReading,
        topUpsSinceLastReading: topUpsSinceLastReading,
        lastReadingDate: lastReadingDate,
        recentAverageUsage: dailyUsage,
        totalAverageUsage: dailyUsage,
      );
    } catch (e) {
      Logger.error('UnifiedAlarmService: Error calculating days to zero: $e');
      return null;
    }
  }

  /// Calculate days until meter reaches threshold using AverageCalculator
  double? _calculateDaysToThreshold(
      Map<String, dynamic> dashboardData, double threshold, int daysInAdvance) {
    try {
      final lastMeterReading = dashboardData['lastMeterReading'] as double?;
      final topUpsSinceLastReading =
          dashboardData['topUpsSinceLastReading'] as double?;
      final lastReadingDate = dashboardData['lastReadingDate'] as DateTime?;
      final dailyUsage = dashboardData['dailyUsage'] as double?;

      if (lastMeterReading == null ||
          topUpsSinceLastReading == null ||
          lastReadingDate == null ||
          dailyUsage == null ||
          dailyUsage <= 0) {
        return null;
      }

      // Use AverageCalculator for consistent calculation across the app
      return AverageCalculator.calculateDaysToAlertThreshold(
        lastMeterReading: lastMeterReading,
        topUpsSinceLastReading: topUpsSinceLastReading,
        lastReadingDate: lastReadingDate,
        alertThreshold: threshold,
        recentAverageUsage: dailyUsage,
        totalAverageUsage: dailyUsage,
        daysInAdvance: daysInAdvance,
      );
    } catch (e) {
      Logger.error(
          'UnifiedAlarmService: Error calculating days to threshold: $e');
      return null;
    }
  }

  /// Calculate next reminder time based on frequency
  DateTime _calculateNextReminderTime(DateTime startTime, String frequency,
      {DateTime? fromTime}) {
    final now = fromTime ?? DateTime.now();

    // Check if this is an immediate reminder (within next 24 hours)
    final timeDifference = startTime.difference(now);
    final isImmediateReminder =
        timeDifference.inHours >= 0 && timeDifference.inHours < 24;

    if (isImmediateReminder && startTime.isAfter(now)) {
      // For immediate reminders, use the exact time set by user
      Logger.info(
          'UnifiedAlarmService: Immediate reminder detected, scheduling for exact time: $startTime');
      return startTime;
    }

    // For recurring reminders, calculate next occurrence
    DateTime nextTime = DateTime(
      now.year,
      now.month,
      now.day,
      startTime.hour,
      startTime.minute,
    );

    // If today's time has passed, start from tomorrow
    if (nextTime.isBefore(now)) {
      nextTime = nextTime.add(const Duration(days: 1));
    }

    switch (frequency.toLowerCase()) {
      case 'daily':
        return nextTime;
      case 'weekly':
        // Find next occurrence of the same day of week
        final targetWeekday = startTime.weekday;
        while (nextTime.weekday != targetWeekday) {
          nextTime = nextTime.add(const Duration(days: 1));
        }
        return nextTime;
      case 'monthly':
        // Find next occurrence of the same day of month
        final targetDay = startTime.day;
        while (nextTime.day != targetDay) {
          nextTime = nextTime.add(const Duration(days: 1));
          // Handle month overflow
          if (nextTime.month != now.month && nextTime.day < targetDay) {
            nextTime = DateTime(nextTime.year, nextTime.month + 1, targetDay,
                startTime.hour, startTime.minute);
          }
        }
        return nextTime;
      default:
        return nextTime;
    }
  }

  /// Schedule an alarm with the given parameters
  Future<void> _scheduleAlarm({
    required int id,
    required DateTime dateTime,
    required String title,
    required String body,
    bool loopAudio = false,
    bool androidFullScreenIntent = false,
  }) async {
    try {
      // Stop any existing alarm with this ID first
      await Alarm.stop(id);

      // Load alarm settings
      final prefs = await SharedPreferences.getInstance();
      final alarmSoundEnabled = prefs.getBool('alarmSoundEnabled') ?? true;
      final alarmVibrationEnabled =
          prefs.getBool('alarmVibrationEnabled') ?? true;

      final alarmSettings = AlarmSettings(
        id: id,
        dateTime: dateTime,
        assetAudioPath: 'assets/alarm_short.mp3',
        loopAudio: loopAudio,
        vibrate: alarmVibrationEnabled,
        volume: alarmSoundEnabled ? 0.8 : 0.0,
        fadeDuration: 3.0,
        notificationTitle: title,
        notificationBody: body,
        enableNotificationOnKill: Platform.isIOS,
        androidFullScreenIntent: androidFullScreenIntent,
      );

      await Alarm.set(alarmSettings: alarmSettings);
      Logger.info('UnifiedAlarmService: Scheduled alarm ID $id for: $dateTime');
    } catch (e) {
      Logger.error('UnifiedAlarmService: Error scheduling alarm ID $id: $e');
      rethrow;
    }
  }

  // Public API methods for external coordination

  /// Schedule meter reading reminder
  Future<void> scheduleMeterReadingReminder() async {
    await _scheduleNextMeterReadingReminder();
  }

  /// Cancel meter reading reminder
  Future<void> cancelMeterReadingReminder() async {
    await Alarm.stop(meterReadingReminderId);
    Logger.info('UnifiedAlarmService: Cancelled meter reading reminder');
  }

  /// Schedule alert checks (low balance and threshold)
  Future<void> scheduleAlertChecks() async {
    await _rescheduleAllAlerts();
  }

  /// Cancel all alert checks
  Future<void> cancelAllAlertChecks() async {
    await Alarm.stop(lowBalanceAlertId);
    await Alarm.stop(thresholdAlertId);
    Logger.info('UnifiedAlarmService: Cancelled all alert checks');
  }

  /// Cancel all alarms
  Future<void> cancelAllAlarms() async {
    await Alarm.stop(meterReadingReminderId);
    await Alarm.stop(lowBalanceAlertId);
    await Alarm.stop(thresholdAlertId);
    Logger.info('UnifiedAlarmService: Cancelled all alarms');
  }

  /// Get status of all alarms
  Future<Map<String, bool>> getAlarmStatus() async {
    final alarms = Alarm.getAlarms();
    return {
      'meterReadingReminder':
          alarms.any((alarm) => alarm.id == meterReadingReminderId),
      'lowBalanceAlert': alarms.any((alarm) => alarm.id == lowBalanceAlertId),
      'thresholdAlert': alarms.any((alarm) => alarm.id == thresholdAlertId),
    };
  }

  /// Force immediate alert check (for testing or manual triggers)
  Future<void> forceAlertCheck() async {
    try {
      Logger.info('UnifiedAlarmService: Forcing immediate alert check');

      final prefs = await SharedPreferences.getInstance();
      final lowBalanceEnabled =
          prefs.getBool(PreferenceKeys.lowBalanceAlertsEnabled) ?? false;
      final thresholdEnabled =
          prefs.getBool(PreferenceKeys.timeToTopUpAlertsEnabled) ?? false;

      Logger.info(
          'UnifiedAlarmService: 🔧 DEBUG - lowBalanceEnabled: $lowBalanceEnabled, thresholdEnabled: $thresholdEnabled');

      // Simulate alarm firing for immediate check
      if (lowBalanceEnabled) {
        Logger.info(
            'UnifiedAlarmService: 🔋 Triggering manual low balance alert');
        _handleLowBalanceAlert(AlarmSettings(
          id: lowBalanceAlertId,
          dateTime: DateTime.now(),
          assetAudioPath: 'assets/alarm_short.mp3',
          notificationTitle: 'Low Balance Alert',
          notificationBody: 'Manual test triggered',
        ));
      }

      if (thresholdEnabled) {
        Logger.info(
            'UnifiedAlarmService: 💰 Triggering manual threshold alert');
        _handleThresholdAlert(AlarmSettings(
          id: thresholdAlertId,
          dateTime: DateTime.now(),
          assetAudioPath: 'assets/alarm_short.mp3',
          notificationTitle: 'Time to Top-Up',
          notificationBody: 'Manual test triggered',
        ));
      }

      if (!lowBalanceEnabled && !thresholdEnabled) {
        Logger.info(
            'UnifiedAlarmService: ❌ No alerts enabled for manual testing');
      }
    } catch (e) {
      Logger.error('UnifiedAlarmService: Error in force alert check: $e');
    }
  }

  /// Dispose resources
  void dispose() {
    _alarmSubscription?.cancel();
    _eventSubscription?.cancel();
    _reminderSchedulingTimer?.cancel();
    _isInitialized = false;
    Logger.info('UnifiedAlarmService: Disposed');
  }
}
