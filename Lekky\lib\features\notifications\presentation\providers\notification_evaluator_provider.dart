import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../../../../core/providers/settings_provider.dart';
import '../../../../core/providers/database_provider.dart';
import '../../../../core/utils/logger.dart';
import '../../../../core/utils/entry_filter_utils.dart';
import '../../../../core/utils/event_bus.dart';
import '../../../../core/utils/average_calculator.dart';
import '../../../meter_readings/domain/models/meter_reading.dart';
import '../../domain/models/notification.dart';
import 'dart:async';

part 'notification_evaluator_provider.g.dart';

/// Provider that evaluates notification conditions independently using raw data
@riverpod
class NotificationEvaluator extends _$NotificationEvaluator {
  StreamSubscription<EventType>? _eventSubscription;

  @override
  Future<List<AppNotification>> build() async {
    // Set up event listener for data updates
    _setupEventListener();
    Logger.info('NotificationEvaluator: Starting notification evaluation');

    // Watch meter readings and settings directly (independent of dashboard)
    final meterReadingRepo = ref.watch(meterReadingRepositoryProvider);
    final topUpRepo = ref.watch(topUpRepositoryProvider);
    final settingsAsync = ref.watch(settingsProvider);

    return await settingsAsync.when(
      data: (settings) async {
        try {
          Logger.info('NotificationEvaluator: Loading data from repositories');

          // Get raw data directly from repositories
          final meterReadings = await meterReadingRepo.getAllMeterReadings();
          final topUps = await topUpRepo.getAllTopUps();

          Logger.info(
              'NotificationEvaluator: Loaded ${meterReadings.length} meter readings and ${topUps.length} top-ups');

          final result = _evaluateConditions(meterReadings, topUps, settings);
          return result;
        } catch (e) {
          Logger.error('NotificationEvaluator: Error loading data: $e');
          return <AppNotification>[];
        }
      },
      loading: () {
        Logger.info('NotificationEvaluator: Settings still loading');
        return <AppNotification>[];
      },
      error: (error, stackTrace) {
        Logger.error('NotificationEvaluator: Settings error: $error');
        return <AppNotification>[];
      },
    );
  }

  /// Evaluate all notification conditions using raw meter reading data
  List<AppNotification> _evaluateConditions(
    List<MeterReading> meterReadings,
    List<dynamic> topUps,
    dynamic settings,
  ) {
    final notifications = <AppNotification>[];

    try {
      // Check low balance condition (< 24 hours to meter zero)
      if (settings.lowBalanceAlertsEnabled) {
        final lowBalanceNotification = _checkLowBalanceCondition(
          meterReadings,
          topUps,
        );
        if (lowBalanceNotification != null) {
          notifications.add(lowBalanceNotification);
        }
      }

      // Check time to top up condition (< 24 hours to alert threshold)
      if (settings.timeToTopUpAlertsEnabled) {
        final topUpNotification = _checkTimeToTopUpCondition(
          meterReadings,
          topUps,
          settings.alertThreshold,
          settings.daysInAdvance,
        );
        if (topUpNotification != null) {
          notifications.add(topUpNotification);
        }
      }

      // Invalid record alerts would be handled separately by validation system
      // as they don't depend on calculations

      Logger.info(
          'NotificationEvaluator: Evaluated ${notifications.length} notifications');
    } catch (e) {
      Logger.error('NotificationEvaluator: Error evaluating conditions: $e');
    }

    return notifications;
  }

  /// Check low balance condition using raw meter reading data
  AppNotification? _checkLowBalanceCondition(
    List<MeterReading> meterReadings,
    List<dynamic> topUps,
  ) {
    try {
      // Calculate days to zero independently
      final daysToZero = _calculateDaysToMeterZero(meterReadings, topUps);

      // Check if condition is met (< 24 hours to zero but not already zero)
      if (daysToZero == null || daysToZero >= 1.0 || daysToZero <= 0) {
        return null;
      }

      final hoursRemaining = (daysToZero * 24).round();
      return AppNotification(
        title: 'Low Balance Alert',
        message: hoursRemaining > 0
            ? 'Your meter will reach zero in approximately $hoursRemaining hours'
            : 'Your meter balance is low',
        timestamp: DateTime.now(),
        type: NotificationType.lowBalance,
      );
    } catch (e) {
      Logger.error('NotificationEvaluator: Error checking low balance: $e');
      return null;
    }
  }

  /// Check time to top up condition using raw meter reading data
  AppNotification? _checkTimeToTopUpCondition(
    List<MeterReading> meterReadings,
    List<dynamic> topUps,
    double alertThreshold,
    int daysInAdvance,
  ) {
    try {
      // Calculate days to threshold independently
      final daysToThreshold = _calculateDaysToAlertThreshold(
        meterReadings,
        topUps,
        alertThreshold,
        daysInAdvance,
      );

      // Check if condition is met (within advance warning period)
      if (daysToThreshold == null) {
        return null;
      }

      final hoursRemaining = (daysToThreshold * 24).round();
      return AppNotification(
        title: 'Time to Top-Up',
        message: hoursRemaining > 0
            ? 'You should top up in approximately $hoursRemaining hours.'
            : 'It\'s time to top up your meter.',
        timestamp: DateTime.now(),
        type: NotificationType.timeToTopUp,
      );
    } catch (e) {
      Logger.error('NotificationEvaluator: Error checking time to top up: $e');
      return null;
    }
  }

  /// Force re-evaluation of conditions
  void refresh() {
    ref.invalidateSelf();
  }

  /// Calculate days to meter zero using AverageCalculator for consistency
  double? _calculateDaysToMeterZero(
    List<MeterReading> meterReadings,
    List<dynamic> topUps,
  ) {
    try {
      // Get the latest valid meter reading
      final latestReading =
          EntryFilterUtils.getLastValidMeterReading(meterReadings);
      if (latestReading == null) {
        return null; // No valid reading
      }

      // Calculate top-ups since latest reading
      final topUpsSinceLatest = topUps
          .where((topUp) => topUp.date.isAfter(latestReading.date))
          .fold<double>(0.0, (sum, topUp) => sum + topUp.amount);

      // Calculate recent average consumption
      final recentAverage = _calculateRecentAverage(meterReadings, topUps);
      if (recentAverage == null || recentAverage <= 0) {
        return null; // No consumption data or no consumption
      }

      // Use AverageCalculator for consistent calculation across the app
      return AverageCalculator.calculateDaysToMeterZero(
        lastMeterReading: latestReading.value,
        topUpsSinceLastReading: topUpsSinceLatest,
        lastReadingDate: latestReading.date,
        recentAverageUsage: recentAverage,
        totalAverageUsage: recentAverage,
      );
    } catch (e) {
      Logger.error('NotificationEvaluator: Error calculating days to zero: $e');
      return null;
    }
  }

  /// Calculate days to alert threshold using AverageCalculator for consistency
  double? _calculateDaysToAlertThreshold(
    List<MeterReading> meterReadings,
    List<dynamic> topUps,
    double alertThreshold,
    int daysInAdvance,
  ) {
    try {
      // Get the latest valid meter reading
      final latestReading =
          EntryFilterUtils.getLastValidMeterReading(meterReadings);
      if (latestReading == null) {
        return null; // No valid reading
      }

      // Calculate top-ups since latest reading
      final topUpsSinceLatest = topUps
          .where((topUp) => topUp.date.isAfter(latestReading.date))
          .fold<double>(0.0, (sum, topUp) => sum + topUp.amount);

      // Calculate recent average consumption
      final recentAverage = _calculateRecentAverage(meterReadings, topUps);
      if (recentAverage == null || recentAverage <= 0) {
        return null; // No consumption data or no consumption
      }

      // Use AverageCalculator for consistent calculation across the app
      final daysToThreshold = AverageCalculator.calculateDaysToAlertThreshold(
        lastMeterReading: latestReading.value,
        topUpsSinceLastReading: topUpsSinceLatest,
        lastReadingDate: latestReading.date,
        alertThreshold: alertThreshold,
        recentAverageUsage: recentAverage,
        totalAverageUsage: recentAverage,
        daysInAdvance: daysInAdvance,
      );

      // Return result if within warning period (AverageCalculator handles the logic)
      return daysToThreshold != null && daysToThreshold >= 0
          ? daysToThreshold
          : null;
    } catch (e) {
      Logger.error(
          'NotificationEvaluator: Error calculating days to threshold: $e');
      return null;
    }
  }

  /// Calculate recent average consumption using raw data
  double? _calculateRecentAverage(
    List<MeterReading> meterReadings,
    List<dynamic> topUps,
  ) {
    try {
      // Use the same logic as dashboard calculations
      // Get valid meter readings sorted by date (newest first)
      final validReadings = meterReadings
          .where((reading) => reading.isValid && reading.value > 0)
          .toList()
        ..sort((a, b) => b.date.compareTo(a.date));

      if (validReadings.length < 2) {
        return null; // Need at least 2 readings for average
      }

      // Take the most recent readings for calculation (last 30 days or 10 readings max)
      final cutoffDate = DateTime.now().subtract(const Duration(days: 30));
      final recentReadings = validReadings
          .where((reading) => reading.date.isAfter(cutoffDate))
          .take(10)
          .toList();

      if (recentReadings.length < 2) {
        return null; // Not enough recent data
      }

      // Calculate consumption between readings
      double totalConsumption = 0;
      double totalDays = 0;

      for (int i = 0; i < recentReadings.length - 1; i++) {
        final current = recentReadings[i];
        final previous = recentReadings[i + 1];

        final daysBetween =
            current.date.difference(previous.date).inDays.toDouble();
        if (daysBetween <= 0) continue;

        // Get top-ups between these readings
        final topUpsBetween = topUps
            .where((topUp) =>
                topUp.date.isAfter(previous.date) &&
                topUp.date.isBefore(current.date.add(const Duration(days: 1))))
            .fold(0.0, (sum, topUp) => sum + (topUp.amount ?? 0.0));

        // Calculate consumption: previous balance + top-ups - current balance
        final consumption = (previous.value + topUpsBetween) - current.value;

        if (consumption > 0) {
          totalConsumption += consumption;
          totalDays += daysBetween;
        }
      }

      if (totalDays <= 0) {
        return null; // No valid consumption periods
      }

      return totalConsumption / totalDays; // Average consumption per day
    } catch (e) {
      Logger.error(
          'NotificationEvaluator: Error calculating recent average: $e');
      return null;
    }
  }

  /// Set up event listener for data updates
  void _setupEventListener() {
    _eventSubscription?.cancel(); // Cancel any existing subscription
    _eventSubscription = EventBus().stream.listen((event) {
      Logger.info('NotificationEvaluator: Received event: $event');
      if (event == EventType.dataUpdated ||
          event == EventType.alertSettingsUpdated) {
        Logger.info(
            'NotificationEvaluator: Data or alert settings updated, invalidating provider');
        // Invalidate provider to trigger re-evaluation
        ref.invalidateSelf();
      }
    });
  }
}
