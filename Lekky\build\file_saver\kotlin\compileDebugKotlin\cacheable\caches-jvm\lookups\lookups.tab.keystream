  SuppressLint android.annotation  Activity android.app  	RESULT_OK android.app.Activity  baseContext android.app.Activity  contentResolver android.app.Activity  getBASEContext android.app.Activity  getBaseContext android.app.Activity  getCONTENTResolver android.app.Activity  getContentResolver android.app.Activity  setBaseContext android.app.Activity  setContentResolver android.app.Activity  startActivityForResult android.app.Activity  ContentUris android.content  Context android.content  Intent android.content  openInputStream android.content.ContentResolver  openOutputStream android.content.ContentResolver  query android.content.ContentResolver  withAppendedId android.content.ContentUris  cacheDir android.content.Context  contentResolver android.content.Context  filesDir android.content.Context  getCACHEDir android.content.Context  getCONTENTResolver android.content.Context  getCacheDir android.content.Context  getContentResolver android.content.Context  getExternalFilesDir android.content.Context  getFILESDir android.content.Context  getFilesDir android.content.Context  setCacheDir android.content.Context  setContentResolver android.content.Context  setFilesDir android.content.Context  startActivityForResult android.content.Context  startActivityForResult android.content.ContextWrapper  ACTION_CREATE_DOCUMENT android.content.Intent  CATEGORY_OPENABLE android.content.Intent  EXTRA_TITLE android.content.Intent  addCategory android.content.Intent  data android.content.Intent  getDATA android.content.Intent  getData android.content.Intent  getTYPE android.content.Intent  getType android.content.Intent  putExtra android.content.Intent  setData android.content.Intent  setType android.content.Intent  type android.content.Intent  Cursor android.database  close android.database.Cursor  equals android.database.Cursor  getColumnIndex android.database.Cursor  getColumnIndexOrThrow android.database.Cursor  	getString android.database.Cursor  moveToFirst android.database.Cursor  Uri android.net  	authority android.net.Uri  equals android.net.Uri  getAUTHORITY android.net.Uri  getAuthority android.net.Uri  getLASTPathSegment android.net.Uri  getLastPathSegment android.net.Uri  getPATH android.net.Uri  getPath android.net.Uri  	getSCHEME android.net.Uri  	getScheme android.net.Uri  lastPathSegment android.net.Uri  parse android.net.Uri  path android.net.Uri  scheme android.net.Uri  setAuthority android.net.Uri  setLastPathSegment android.net.Uri  setPath android.net.Uri  	setScheme android.net.Uri  Build 
android.os  Environment 
android.os  VERSION android.os.Build  
VERSION_CODES android.os.Build  SDK_INT android.os.Build.VERSION  KITKAT android.os.Build.VERSION_CODES  M android.os.Build.VERSION_CODES  Q android.os.Build.VERSION_CODES  getExternalStorageDirectory android.os.Environment  DocumentsContract android.provider  
MediaStore android.provider  OpenableColumns android.provider  EXTRA_INITIAL_URI "android.provider.DocumentsContract  
getDocumentId "android.provider.DocumentsContract  Audio android.provider.MediaStore  Images android.provider.MediaStore  MediaColumns android.provider.MediaStore  Video android.provider.MediaStore  Media !android.provider.MediaStore.Audio  EXTERNAL_CONTENT_URI 'android.provider.MediaStore.Audio.Media  Media "android.provider.MediaStore.Images  DATA (android.provider.MediaStore.Images.Media  EXTERNAL_CONTENT_URI (android.provider.MediaStore.Images.Media  DISPLAY_NAME (android.provider.MediaStore.MediaColumns  Media !android.provider.MediaStore.Video  EXTERNAL_CONTENT_URI 'android.provider.MediaStore.Video.Media  DISPLAY_NAME  android.provider.OpenableColumns  SIZE  android.provider.OpenableColumns  	TextUtils android.text  isEmpty android.text.TextUtils  Log android.util  d android.util.Log  e android.util.Log  startActivityForResult  android.view.ContextThemeWrapper  NonNull androidx.annotation  RequiresApi androidx.annotation  ActivityCompat androidx.core.app  Activity com.incrediblezayed.file_saver  Array com.incrediblezayed.file_saver  Boolean com.incrediblezayed.file_saver  Build com.incrediblezayed.file_saver  	ByteArray com.incrediblezayed.file_saver  ContentUris com.incrediblezayed.file_saver  CoroutineScope com.incrediblezayed.file_saver  Dialog com.incrediblezayed.file_saver  Dispatchers com.incrediblezayed.file_saver  DocumentsContract com.incrediblezayed.file_saver  Environment com.incrediblezayed.file_saver  	Exception com.incrediblezayed.file_saver  File com.incrediblezayed.file_saver  FileOutputStream com.incrediblezayed.file_saver  FileSaverPlugin com.incrediblezayed.file_saver  	FileUtils com.incrediblezayed.file_saver  Int com.incrediblezayed.file_saver  Intent com.incrediblezayed.file_saver  Log com.incrediblezayed.file_saver  Math com.incrediblezayed.file_saver  
MediaStore com.incrediblezayed.file_saver  
MethodChannel com.incrediblezayed.file_saver  OpenableColumns com.incrediblezayed.file_saver  	SAVE_FILE com.incrediblezayed.file_saver  SecurityException com.incrediblezayed.file_saver  String com.incrediblezayed.file_saver  System com.incrediblezayed.file_saver  TAG com.incrediblezayed.file_saver  	TextUtils com.incrediblezayed.file_saver  Uri com.incrediblezayed.file_saver  activity com.incrediblezayed.file_saver  also com.incrediblezayed.file_saver  arrayOf com.incrediblezayed.file_saver  
contentUri com.incrediblezayed.file_saver  equals com.incrediblezayed.file_saver  invoke com.incrediblezayed.file_saver  java com.incrediblezayed.file_saver  launch com.incrediblezayed.file_saver  replaceFirst com.incrediblezayed.file_saver  result com.incrediblezayed.file_saver  saveFile com.incrediblezayed.file_saver  split com.incrediblezayed.file_saver  
startsWith com.incrediblezayed.file_saver  toRegex com.incrediblezayed.file_saver  toTypedArray com.incrediblezayed.file_saver  
writeBytes com.incrediblezayed.file_saver  Activity %com.incrediblezayed.file_saver.Dialog  Boolean %com.incrediblezayed.file_saver.Dialog  	ByteArray %com.incrediblezayed.file_saver.Dialog  CoroutineScope %com.incrediblezayed.file_saver.Dialog  Dispatchers %com.incrediblezayed.file_saver.Dialog  DocumentsContract %com.incrediblezayed.file_saver.Dialog  Environment %com.incrediblezayed.file_saver.Dialog  	Exception %com.incrediblezayed.file_saver.Dialog  	FileUtils %com.incrediblezayed.file_saver.Dialog  Int %com.incrediblezayed.file_saver.Dialog  Intent %com.incrediblezayed.file_saver.Dialog  Log %com.incrediblezayed.file_saver.Dialog  
MethodChannel %com.incrediblezayed.file_saver.Dialog  	SAVE_FILE %com.incrediblezayed.file_saver.Dialog  SecurityException %com.incrediblezayed.file_saver.Dialog  String %com.incrediblezayed.file_saver.Dialog  TAG %com.incrediblezayed.file_saver.Dialog  Uri %com.incrediblezayed.file_saver.Dialog  activity %com.incrediblezayed.file_saver.Dialog  bytes %com.incrediblezayed.file_saver.Dialog  completeFileOperation %com.incrediblezayed.file_saver.Dialog  equals %com.incrediblezayed.file_saver.Dialog  fileName %com.incrediblezayed.file_saver.Dialog  	getLAUNCH %com.incrediblezayed.file_saver.Dialog  	getLaunch %com.incrediblezayed.file_saver.Dialog  invoke %com.incrediblezayed.file_saver.Dialog  launch %com.incrediblezayed.file_saver.Dialog  openFileManager %com.incrediblezayed.file_saver.Dialog  result %com.incrediblezayed.file_saver.Dialog  saveFile %com.incrediblezayed.file_saver.Dialog  ActivityPluginBinding .com.incrediblezayed.file_saver.FileSaverPlugin  Boolean .com.incrediblezayed.file_saver.FileSaverPlugin  	ByteArray .com.incrediblezayed.file_saver.FileSaverPlugin  Dialog .com.incrediblezayed.file_saver.FileSaverPlugin  	Exception .com.incrediblezayed.file_saver.FileSaverPlugin  File .com.incrediblezayed.file_saver.FileSaverPlugin  
FlutterPlugin .com.incrediblezayed.file_saver.FileSaverPlugin  Log .com.incrediblezayed.file_saver.FileSaverPlugin  
MethodCall .com.incrediblezayed.file_saver.FileSaverPlugin  
MethodChannel .com.incrediblezayed.file_saver.FileSaverPlugin  NonNull .com.incrediblezayed.file_saver.FileSaverPlugin  Result .com.incrediblezayed.file_saver.FileSaverPlugin  String .com.incrediblezayed.file_saver.FileSaverPlugin  activity .com.incrediblezayed.file_saver.FileSaverPlugin  createFileDialog .com.incrediblezayed.file_saver.FileSaverPlugin  dialog .com.incrediblezayed.file_saver.FileSaverPlugin  
getWRITEBytes .com.incrediblezayed.file_saver.FileSaverPlugin  
getWriteBytes .com.incrediblezayed.file_saver.FileSaverPlugin  
methodChannel .com.incrediblezayed.file_saver.FileSaverPlugin  
pluginBinding .com.incrediblezayed.file_saver.FileSaverPlugin  result .com.incrediblezayed.file_saver.FileSaverPlugin  saveFile .com.incrediblezayed.file_saver.FileSaverPlugin  tag .com.incrediblezayed.file_saver.FileSaverPlugin  
writeBytes .com.incrediblezayed.file_saver.FileSaverPlugin  Array (com.incrediblezayed.file_saver.FileUtils  Boolean (com.incrediblezayed.file_saver.FileUtils  Build (com.incrediblezayed.file_saver.FileUtils  	ByteArray (com.incrediblezayed.file_saver.FileUtils  ContentUris (com.incrediblezayed.file_saver.FileUtils  Context (com.incrediblezayed.file_saver.FileUtils  Cursor (com.incrediblezayed.file_saver.FileUtils  DocumentsContract (com.incrediblezayed.file_saver.FileUtils  Environment (com.incrediblezayed.file_saver.FileUtils  	Exception (com.incrediblezayed.file_saver.FileUtils  File (com.incrediblezayed.file_saver.FileUtils  FileOutputStream (com.incrediblezayed.file_saver.FileUtils  Log (com.incrediblezayed.file_saver.FileUtils  Math (com.incrediblezayed.file_saver.FileUtils  
MediaStore (com.incrediblezayed.file_saver.FileUtils  NumberFormatException (com.incrediblezayed.file_saver.FileUtils  OpenableColumns (com.incrediblezayed.file_saver.FileUtils  String (com.incrediblezayed.file_saver.FileUtils  SuppressLint (com.incrediblezayed.file_saver.FileUtils  System (com.incrediblezayed.file_saver.FileUtils  	TextUtils (com.incrediblezayed.file_saver.FileUtils  Uri (com.incrediblezayed.file_saver.FileUtils  also (com.incrediblezayed.file_saver.FileUtils  arrayOf (com.incrediblezayed.file_saver.FileUtils  
contentUri (com.incrediblezayed.file_saver.FileUtils  context (com.incrediblezayed.file_saver.FileUtils  copyFileToInternalStorage (com.incrediblezayed.file_saver.FileUtils  equals (com.incrediblezayed.file_saver.FileUtils  
fileExists (com.incrediblezayed.file_saver.FileUtils  getALSO (com.incrediblezayed.file_saver.FileUtils  
getARRAYOf (com.incrediblezayed.file_saver.FileUtils  getAlso (com.incrediblezayed.file_saver.FileUtils  
getArrayOf (com.incrediblezayed.file_saver.FileUtils  
getCONTENTUri (com.incrediblezayed.file_saver.FileUtils  
getContentUri (com.incrediblezayed.file_saver.FileUtils  
getDataColumn (com.incrediblezayed.file_saver.FileUtils  getDriveFilePath (com.incrediblezayed.file_saver.FileUtils  	getEQUALS (com.incrediblezayed.file_saver.FileUtils  	getEquals (com.incrediblezayed.file_saver.FileUtils  getFilePathForWhatsApp (com.incrediblezayed.file_saver.FileUtils  getJAVA (com.incrediblezayed.file_saver.FileUtils  getJava (com.incrediblezayed.file_saver.FileUtils  getPath (com.incrediblezayed.file_saver.FileUtils  getPathFromExtSD (com.incrediblezayed.file_saver.FileUtils  getREPLACEFirst (com.incrediblezayed.file_saver.FileUtils  getReplaceFirst (com.incrediblezayed.file_saver.FileUtils  getSPLIT (com.incrediblezayed.file_saver.FileUtils  
getSTARTSWith (com.incrediblezayed.file_saver.FileUtils  getSplit (com.incrediblezayed.file_saver.FileUtils  
getStartsWith (com.incrediblezayed.file_saver.FileUtils  
getTORegex (com.incrediblezayed.file_saver.FileUtils  getTOTypedArray (com.incrediblezayed.file_saver.FileUtils  
getToRegex (com.incrediblezayed.file_saver.FileUtils  getToTypedArray (com.incrediblezayed.file_saver.FileUtils  isDownloadsDocument (com.incrediblezayed.file_saver.FileUtils  isExternalStorageDocument (com.incrediblezayed.file_saver.FileUtils  isGoogleDriveUri (com.incrediblezayed.file_saver.FileUtils  isGooglePhotosUri (com.incrediblezayed.file_saver.FileUtils  isMediaDocument (com.incrediblezayed.file_saver.FileUtils  isWhatsAppFile (com.incrediblezayed.file_saver.FileUtils  java (com.incrediblezayed.file_saver.FileUtils  replaceFirst (com.incrediblezayed.file_saver.FileUtils  split (com.incrediblezayed.file_saver.FileUtils  
startsWith (com.incrediblezayed.file_saver.FileUtils  toRegex (com.incrediblezayed.file_saver.FileUtils  toTypedArray (com.incrediblezayed.file_saver.FileUtils  Array 2com.incrediblezayed.file_saver.FileUtils.Companion  Boolean 2com.incrediblezayed.file_saver.FileUtils.Companion  Build 2com.incrediblezayed.file_saver.FileUtils.Companion  	ByteArray 2com.incrediblezayed.file_saver.FileUtils.Companion  ContentUris 2com.incrediblezayed.file_saver.FileUtils.Companion  Context 2com.incrediblezayed.file_saver.FileUtils.Companion  Cursor 2com.incrediblezayed.file_saver.FileUtils.Companion  DocumentsContract 2com.incrediblezayed.file_saver.FileUtils.Companion  Environment 2com.incrediblezayed.file_saver.FileUtils.Companion  	Exception 2com.incrediblezayed.file_saver.FileUtils.Companion  File 2com.incrediblezayed.file_saver.FileUtils.Companion  FileOutputStream 2com.incrediblezayed.file_saver.FileUtils.Companion  Log 2com.incrediblezayed.file_saver.FileUtils.Companion  Math 2com.incrediblezayed.file_saver.FileUtils.Companion  
MediaStore 2com.incrediblezayed.file_saver.FileUtils.Companion  NumberFormatException 2com.incrediblezayed.file_saver.FileUtils.Companion  OpenableColumns 2com.incrediblezayed.file_saver.FileUtils.Companion  String 2com.incrediblezayed.file_saver.FileUtils.Companion  SuppressLint 2com.incrediblezayed.file_saver.FileUtils.Companion  System 2com.incrediblezayed.file_saver.FileUtils.Companion  	TextUtils 2com.incrediblezayed.file_saver.FileUtils.Companion  Uri 2com.incrediblezayed.file_saver.FileUtils.Companion  also 2com.incrediblezayed.file_saver.FileUtils.Companion  arrayOf 2com.incrediblezayed.file_saver.FileUtils.Companion  
contentUri 2com.incrediblezayed.file_saver.FileUtils.Companion  equals 2com.incrediblezayed.file_saver.FileUtils.Companion  getALSO 2com.incrediblezayed.file_saver.FileUtils.Companion  
getARRAYOf 2com.incrediblezayed.file_saver.FileUtils.Companion  getAlso 2com.incrediblezayed.file_saver.FileUtils.Companion  
getArrayOf 2com.incrediblezayed.file_saver.FileUtils.Companion  	getEQUALS 2com.incrediblezayed.file_saver.FileUtils.Companion  	getEquals 2com.incrediblezayed.file_saver.FileUtils.Companion  getJAVA 2com.incrediblezayed.file_saver.FileUtils.Companion  getJava 2com.incrediblezayed.file_saver.FileUtils.Companion  getREPLACEFirst 2com.incrediblezayed.file_saver.FileUtils.Companion  getReplaceFirst 2com.incrediblezayed.file_saver.FileUtils.Companion  getSPLIT 2com.incrediblezayed.file_saver.FileUtils.Companion  
getSTARTSWith 2com.incrediblezayed.file_saver.FileUtils.Companion  getSplit 2com.incrediblezayed.file_saver.FileUtils.Companion  
getStartsWith 2com.incrediblezayed.file_saver.FileUtils.Companion  
getTORegex 2com.incrediblezayed.file_saver.FileUtils.Companion  getTOTypedArray 2com.incrediblezayed.file_saver.FileUtils.Companion  
getToRegex 2com.incrediblezayed.file_saver.FileUtils.Companion  getToTypedArray 2com.incrediblezayed.file_saver.FileUtils.Companion  invoke 2com.incrediblezayed.file_saver.FileUtils.Companion  java 2com.incrediblezayed.file_saver.FileUtils.Companion  replaceFirst 2com.incrediblezayed.file_saver.FileUtils.Companion  split 2com.incrediblezayed.file_saver.FileUtils.Companion  
startsWith 2com.incrediblezayed.file_saver.FileUtils.Companion  toRegex 2com.incrediblezayed.file_saver.FileUtils.Companion  toTypedArray 2com.incrediblezayed.file_saver.FileUtils.Companion  
FlutterPlugin #io.flutter.embedding.engine.plugins  FlutterPluginBinding 1io.flutter.embedding.engine.plugins.FlutterPlugin  binaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  equals Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getBINARYMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getBinaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  setBinaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  
ActivityAware ,io.flutter.embedding.engine.plugins.activity  ActivityPluginBinding ,io.flutter.embedding.engine.plugins.activity  activity Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  addActivityResultListener Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  equals Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  getACTIVITY Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  getActivity Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  removeActivityResultListener Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  setActivity Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  BinaryMessenger io.flutter.plugin.common  
MethodCall io.flutter.plugin.common  
MethodChannel io.flutter.plugin.common  PluginRegistry io.flutter.plugin.common  argument #io.flutter.plugin.common.MethodCall  method #io.flutter.plugin.common.MethodCall  MethodCallHandler &io.flutter.plugin.common.MethodChannel  Result &io.flutter.plugin.common.MethodChannel  setMethodCallHandler &io.flutter.plugin.common.MethodChannel  equals -io.flutter.plugin.common.MethodChannel.Result  error -io.flutter.plugin.common.MethodChannel.Result  notImplemented -io.flutter.plugin.common.MethodChannel.Result  success -io.flutter.plugin.common.MethodChannel.Result  ActivityResultListener 'io.flutter.plugin.common.PluginRegistry  File java.io  FileOutputStream java.io  InputStream java.io  OutputStream java.io  absolutePath java.io.File  exists java.io.File  getABSOLUTEPath java.io.File  getAbsolutePath java.io.File  getNAME java.io.File  getName java.io.File  getPATH java.io.File  getPath java.io.File  
getWRITEBytes java.io.File  
getWriteBytes java.io.File  length java.io.File  mkdir java.io.File  name java.io.File  path java.io.File  setAbsolutePath java.io.File  setName java.io.File  setPath java.io.File  toString java.io.File  
writeBytes java.io.File  close java.io.FileOutputStream  write java.io.FileOutputStream  	available java.io.InputStream  close java.io.InputStream  read java.io.InputStream  close java.io.OutputStream  write java.io.OutputStream  Activity 	java.lang  Build 	java.lang  	ByteArray 	java.lang  ContentUris 	java.lang  CoroutineScope 	java.lang  Dialog 	java.lang  Dispatchers 	java.lang  DocumentsContract 	java.lang  Environment 	java.lang  	Exception 	java.lang  File 	java.lang  FileOutputStream 	java.lang  	FileUtils 	java.lang  Intent 	java.lang  Log 	java.lang  Long 	java.lang  Math 	java.lang  
MediaStore 	java.lang  
MethodChannel 	java.lang  NumberFormatException 	java.lang  OpenableColumns 	java.lang  	SAVE_FILE 	java.lang  SecurityException 	java.lang  System 	java.lang  TAG 	java.lang  	TextUtils 	java.lang  Uri 	java.lang  activity 	java.lang  also 	java.lang  arrayOf 	java.lang  
contentUri 	java.lang  equals 	java.lang  java 	java.lang  launch 	java.lang  replaceFirst 	java.lang  result 	java.lang  saveFile 	java.lang  split 	java.lang  
startsWith 	java.lang  toRegex 	java.lang  toTypedArray 	java.lang  
writeBytes 	java.lang  getLOCALIZEDMessage java.lang.Exception  getLocalizedMessage java.lang.Exception  localizedMessage java.lang.Exception  message java.lang.Exception  printStackTrace java.lang.Exception  setLocalizedMessage java.lang.Exception  printStackTrace "java.lang.IllegalArgumentException  valueOf java.lang.Long  min java.lang.Math  printStackTrace java.lang.NumberFormatException  printStackTrace java.lang.RuntimeException  getLOCALIZEDMessage java.lang.SecurityException  getLocalizedMessage java.lang.SecurityException  localizedMessage java.lang.SecurityException  message java.lang.SecurityException  setLocalizedMessage java.lang.SecurityException  getenv java.lang.System  Manifest 
java.util.jar  Activity kotlin  Any kotlin  Array kotlin  Boolean kotlin  Build kotlin  	ByteArray kotlin  ContentUris kotlin  CoroutineScope kotlin  Dialog kotlin  Dispatchers kotlin  DocumentsContract kotlin  Environment kotlin  	Exception kotlin  File kotlin  FileOutputStream kotlin  	FileUtils kotlin  	Function1 kotlin  Int kotlin  Intent kotlin  Log kotlin  Long kotlin  Math kotlin  
MediaStore kotlin  
MethodChannel kotlin  Nothing kotlin  OpenableColumns kotlin  	SAVE_FILE kotlin  SecurityException kotlin  String kotlin  System kotlin  TAG kotlin  	TextUtils kotlin  Unit kotlin  Uri kotlin  activity kotlin  also kotlin  arrayOf kotlin  
contentUri kotlin  equals kotlin  java kotlin  launch kotlin  replaceFirst kotlin  result kotlin  saveFile kotlin  split kotlin  
startsWith kotlin  toRegex kotlin  toTypedArray kotlin  
writeBytes kotlin  getALSO 
kotlin.Int  getAlso 
kotlin.Int  	getEQUALS 
kotlin.String  	getEquals 
kotlin.String  getREPLACEFirst 
kotlin.String  getReplaceFirst 
kotlin.String  getSPLIT 
kotlin.String  
getSTARTSWith 
kotlin.String  getSplit 
kotlin.String  
getStartsWith 
kotlin.String  
getTORegex 
kotlin.String  
getToRegex 
kotlin.String  Activity kotlin.annotation  Build kotlin.annotation  	ByteArray kotlin.annotation  ContentUris kotlin.annotation  CoroutineScope kotlin.annotation  Dialog kotlin.annotation  Dispatchers kotlin.annotation  DocumentsContract kotlin.annotation  Environment kotlin.annotation  	Exception kotlin.annotation  File kotlin.annotation  FileOutputStream kotlin.annotation  	FileUtils kotlin.annotation  Intent kotlin.annotation  Log kotlin.annotation  Math kotlin.annotation  
MediaStore kotlin.annotation  
MethodChannel kotlin.annotation  OpenableColumns kotlin.annotation  	SAVE_FILE kotlin.annotation  SecurityException kotlin.annotation  System kotlin.annotation  TAG kotlin.annotation  	TextUtils kotlin.annotation  Uri kotlin.annotation  activity kotlin.annotation  also kotlin.annotation  arrayOf kotlin.annotation  
contentUri kotlin.annotation  equals kotlin.annotation  java kotlin.annotation  launch kotlin.annotation  replaceFirst kotlin.annotation  result kotlin.annotation  saveFile kotlin.annotation  split kotlin.annotation  
startsWith kotlin.annotation  toRegex kotlin.annotation  toTypedArray kotlin.annotation  
writeBytes kotlin.annotation  Activity kotlin.collections  Build kotlin.collections  	ByteArray kotlin.collections  ContentUris kotlin.collections  CoroutineScope kotlin.collections  Dialog kotlin.collections  Dispatchers kotlin.collections  DocumentsContract kotlin.collections  Environment kotlin.collections  	Exception kotlin.collections  File kotlin.collections  FileOutputStream kotlin.collections  	FileUtils kotlin.collections  Intent kotlin.collections  Log kotlin.collections  Math kotlin.collections  
MediaStore kotlin.collections  
MethodChannel kotlin.collections  OpenableColumns kotlin.collections  	SAVE_FILE kotlin.collections  SecurityException kotlin.collections  System kotlin.collections  TAG kotlin.collections  	TextUtils kotlin.collections  Uri kotlin.collections  activity kotlin.collections  also kotlin.collections  arrayOf kotlin.collections  
contentUri kotlin.collections  equals kotlin.collections  java kotlin.collections  launch kotlin.collections  replaceFirst kotlin.collections  result kotlin.collections  saveFile kotlin.collections  split kotlin.collections  
startsWith kotlin.collections  toRegex kotlin.collections  toTypedArray kotlin.collections  
writeBytes kotlin.collections  getTOTypedArray kotlin.collections.List  getToTypedArray kotlin.collections.List  Activity kotlin.comparisons  Build kotlin.comparisons  	ByteArray kotlin.comparisons  ContentUris kotlin.comparisons  CoroutineScope kotlin.comparisons  Dialog kotlin.comparisons  Dispatchers kotlin.comparisons  DocumentsContract kotlin.comparisons  Environment kotlin.comparisons  	Exception kotlin.comparisons  File kotlin.comparisons  FileOutputStream kotlin.comparisons  	FileUtils kotlin.comparisons  Intent kotlin.comparisons  Log kotlin.comparisons  Math kotlin.comparisons  
MediaStore kotlin.comparisons  
MethodChannel kotlin.comparisons  OpenableColumns kotlin.comparisons  	SAVE_FILE kotlin.comparisons  SecurityException kotlin.comparisons  System kotlin.comparisons  TAG kotlin.comparisons  	TextUtils kotlin.comparisons  Uri kotlin.comparisons  activity kotlin.comparisons  also kotlin.comparisons  arrayOf kotlin.comparisons  
contentUri kotlin.comparisons  equals kotlin.comparisons  java kotlin.comparisons  launch kotlin.comparisons  replaceFirst kotlin.comparisons  result kotlin.comparisons  saveFile kotlin.comparisons  split kotlin.comparisons  
startsWith kotlin.comparisons  toRegex kotlin.comparisons  toTypedArray kotlin.comparisons  
writeBytes kotlin.comparisons  SuspendFunction1 kotlin.coroutines  Activity 	kotlin.io  Build 	kotlin.io  	ByteArray 	kotlin.io  ContentUris 	kotlin.io  CoroutineScope 	kotlin.io  Dialog 	kotlin.io  Dispatchers 	kotlin.io  DocumentsContract 	kotlin.io  Environment 	kotlin.io  	Exception 	kotlin.io  File 	kotlin.io  FileOutputStream 	kotlin.io  	FileUtils 	kotlin.io  Intent 	kotlin.io  Log 	kotlin.io  Math 	kotlin.io  
MediaStore 	kotlin.io  
MethodChannel 	kotlin.io  OpenableColumns 	kotlin.io  	SAVE_FILE 	kotlin.io  SecurityException 	kotlin.io  System 	kotlin.io  TAG 	kotlin.io  	TextUtils 	kotlin.io  Uri 	kotlin.io  activity 	kotlin.io  also 	kotlin.io  arrayOf 	kotlin.io  
contentUri 	kotlin.io  equals 	kotlin.io  java 	kotlin.io  launch 	kotlin.io  replaceFirst 	kotlin.io  result 	kotlin.io  saveFile 	kotlin.io  split 	kotlin.io  
startsWith 	kotlin.io  toRegex 	kotlin.io  toTypedArray 	kotlin.io  
writeBytes 	kotlin.io  Activity 
kotlin.jvm  Build 
kotlin.jvm  	ByteArray 
kotlin.jvm  ContentUris 
kotlin.jvm  CoroutineScope 
kotlin.jvm  Dialog 
kotlin.jvm  Dispatchers 
kotlin.jvm  DocumentsContract 
kotlin.jvm  Environment 
kotlin.jvm  	Exception 
kotlin.jvm  File 
kotlin.jvm  FileOutputStream 
kotlin.jvm  	FileUtils 
kotlin.jvm  Intent 
kotlin.jvm  Log 
kotlin.jvm  Math 
kotlin.jvm  
MediaStore 
kotlin.jvm  
MethodChannel 
kotlin.jvm  OpenableColumns 
kotlin.jvm  	SAVE_FILE 
kotlin.jvm  SecurityException 
kotlin.jvm  System 
kotlin.jvm  TAG 
kotlin.jvm  	TextUtils 
kotlin.jvm  Uri 
kotlin.jvm  activity 
kotlin.jvm  also 
kotlin.jvm  arrayOf 
kotlin.jvm  
contentUri 
kotlin.jvm  equals 
kotlin.jvm  java 
kotlin.jvm  launch 
kotlin.jvm  replaceFirst 
kotlin.jvm  result 
kotlin.jvm  saveFile 
kotlin.jvm  split 
kotlin.jvm  
startsWith 
kotlin.jvm  toRegex 
kotlin.jvm  toTypedArray 
kotlin.jvm  
writeBytes 
kotlin.jvm  Activity 
kotlin.ranges  Build 
kotlin.ranges  	ByteArray 
kotlin.ranges  ContentUris 
kotlin.ranges  CoroutineScope 
kotlin.ranges  Dialog 
kotlin.ranges  Dispatchers 
kotlin.ranges  DocumentsContract 
kotlin.ranges  Environment 
kotlin.ranges  	Exception 
kotlin.ranges  File 
kotlin.ranges  FileOutputStream 
kotlin.ranges  	FileUtils 
kotlin.ranges  Intent 
kotlin.ranges  Log 
kotlin.ranges  Math 
kotlin.ranges  
MediaStore 
kotlin.ranges  
MethodChannel 
kotlin.ranges  OpenableColumns 
kotlin.ranges  	SAVE_FILE 
kotlin.ranges  SecurityException 
kotlin.ranges  System 
kotlin.ranges  TAG 
kotlin.ranges  	TextUtils 
kotlin.ranges  Uri 
kotlin.ranges  activity 
kotlin.ranges  also 
kotlin.ranges  arrayOf 
kotlin.ranges  
contentUri 
kotlin.ranges  equals 
kotlin.ranges  java 
kotlin.ranges  launch 
kotlin.ranges  replaceFirst 
kotlin.ranges  result 
kotlin.ranges  saveFile 
kotlin.ranges  split 
kotlin.ranges  
startsWith 
kotlin.ranges  toRegex 
kotlin.ranges  toTypedArray 
kotlin.ranges  
writeBytes 
kotlin.ranges  Activity kotlin.sequences  Build kotlin.sequences  	ByteArray kotlin.sequences  ContentUris kotlin.sequences  CoroutineScope kotlin.sequences  Dialog kotlin.sequences  Dispatchers kotlin.sequences  DocumentsContract kotlin.sequences  Environment kotlin.sequences  	Exception kotlin.sequences  File kotlin.sequences  FileOutputStream kotlin.sequences  	FileUtils kotlin.sequences  Intent kotlin.sequences  Log kotlin.sequences  Math kotlin.sequences  
MediaStore kotlin.sequences  
MethodChannel kotlin.sequences  OpenableColumns kotlin.sequences  	SAVE_FILE kotlin.sequences  SecurityException kotlin.sequences  System kotlin.sequences  TAG kotlin.sequences  	TextUtils kotlin.sequences  Uri kotlin.sequences  activity kotlin.sequences  also kotlin.sequences  arrayOf kotlin.sequences  
contentUri kotlin.sequences  equals kotlin.sequences  java kotlin.sequences  launch kotlin.sequences  replaceFirst kotlin.sequences  result kotlin.sequences  saveFile kotlin.sequences  split kotlin.sequences  
startsWith kotlin.sequences  toRegex kotlin.sequences  toTypedArray kotlin.sequences  
writeBytes kotlin.sequences  Activity kotlin.text  Build kotlin.text  	ByteArray kotlin.text  ContentUris kotlin.text  CoroutineScope kotlin.text  Dialog kotlin.text  Dispatchers kotlin.text  DocumentsContract kotlin.text  Environment kotlin.text  	Exception kotlin.text  File kotlin.text  FileOutputStream kotlin.text  	FileUtils kotlin.text  Intent kotlin.text  Log kotlin.text  Math kotlin.text  
MediaStore kotlin.text  
MethodChannel kotlin.text  OpenableColumns kotlin.text  Regex kotlin.text  	SAVE_FILE kotlin.text  SecurityException kotlin.text  System kotlin.text  TAG kotlin.text  	TextUtils kotlin.text  Uri kotlin.text  activity kotlin.text  also kotlin.text  arrayOf kotlin.text  
contentUri kotlin.text  equals kotlin.text  java kotlin.text  launch kotlin.text  replaceFirst kotlin.text  result kotlin.text  saveFile kotlin.text  split kotlin.text  
startsWith kotlin.text  toRegex kotlin.text  toTypedArray kotlin.text  
writeBytes kotlin.text  CoroutineScope kotlinx.coroutines  Dispatchers kotlinx.coroutines  Job kotlinx.coroutines  MainCoroutineDispatcher kotlinx.coroutines  launch kotlinx.coroutines  	FileUtils !kotlinx.coroutines.CoroutineScope  Log !kotlinx.coroutines.CoroutineScope  TAG !kotlinx.coroutines.CoroutineScope  activity !kotlinx.coroutines.CoroutineScope  getACTIVITY !kotlinx.coroutines.CoroutineScope  getActivity !kotlinx.coroutines.CoroutineScope  	getLAUNCH !kotlinx.coroutines.CoroutineScope  	getLaunch !kotlinx.coroutines.CoroutineScope  	getRESULT !kotlinx.coroutines.CoroutineScope  	getResult !kotlinx.coroutines.CoroutineScope  getSAVEFile !kotlinx.coroutines.CoroutineScope  getSaveFile !kotlinx.coroutines.CoroutineScope  invoke !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  result !kotlinx.coroutines.CoroutineScope  saveFile !kotlinx.coroutines.CoroutineScope  Main kotlinx.coroutines.Dispatchers                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              